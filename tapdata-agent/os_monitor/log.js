/**
 * <AUTHOR>
 * @date 12/11/19
 * @description
 */
const path = require('path');
const appConfig = require('./config');
let log4jsHttp = require('./log4js-http');
//const logPath = path.join(path.resolve(path.dirname(path.dirname(__dirname))), appConfig.logDir || 'logs');
const logPath = path.join(path.resolve(path.dirname('.')));//, appConfig.logDir || 'logs');
const log4js = require('log4js');
console.info(path.join(__dirname, 'log4js-http'));
log4js.configure({
  appenders: {
    app: {
      type: 'file',
      filename: path.join(logPath,'app.log'),// logPath + '\\app.log',
      maxLogSize: 500 * 1024 * 1024,
      backups: 5,
      compress: true
    },
    /*monitor: {
      type: 'file',
      filename: logPath + '/monitor.log',
      maxLogSize: 500 * 1024 * 1024,
      backups: 5,
      compress: true
    },
    generator: {
      type: 'file',
      filename: logPath + '/generator.log',
      maxLogSize: 500 * 1024 * 1024,
      backups: 5,
      compress: true
    },*/
    out: {
      type: 'stdout'
    },
    http: {
      type:log4jsHttp,//path.join(__dirname, 'log4js-http'),// path.join(path.dirname(process.execPath),'etc', 'log4js-http'),
      application: 'os-monitor',
      url: appConfig.tapDataServer.logUrl
    }
  },
  categories: {
    default: {
      appenders: ['app', 'out', 'http'],
      level: 'info'
    },
    app: {
      appenders: ['app', 'out', 'http'],
      level: 'info'
    }/*,
		monitor: {
			appenders: ['monitor', 'out'],
			level: 'info'
		},
		generator: {
			appenders: ['generator', 'out'],
			level: 'info'
		}*/
  },
});

module.exports = {
  default: log4js.getLogger(),
  app: log4js.getLogger('app'),
  monitor: log4js.getLogger('app'),
  generator: log4js.getLogger('app')
};
