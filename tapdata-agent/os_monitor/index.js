const os = require('os');
const appConfig = require('./config');
const report = require('./report');
const log = require('./log').app;
const uuidv4 = require('uuid/v4');
// const uuidv1 = require('uuid/v1');
const fs = require('fs');
const si = require('systeminformation');
const path = require('path');
let tapdata_work_dir = process.env['TAPDATA_WORK_DIR'] || path.join(os.homedir(), '.tapdata');
tapdata_work_dir = path.join(tapdata_work_dir, 'os-monitor');
const fs_types = [
    'ADFS', 'ADVFS', 'AMIGA FFS', 'AMIGA OFS', 'APFS', 'ATHFS', 'BFS', 'BTRFS', 'DFS', 'EFS', 'EPISODE',
    'EXT', 'EXT2', 'EXT3', 'EXT3COW', 'EXT4', 'FAT', 'EXFAT', 'FILES-11', 'FOSSIL', 'HAMMER', 'HFS', 'HFS+',
    'HPFS', 'HTFS', 'JFS', 'LFS', 'MFS', 'MINIX', 'NETWARE', 'NEXT3', 'NILFS', 'NILFS2', 'NSS', 'NTFS', 'ONEFS',
    'PFS', 'QFS', 'QNX4FS', 'REFS', 'REISERFS', 'REISER4', 'RELIANCE', 'RELIANCE NITRO', 'RFS', 'SFS', 'SOUP',
    'TUX3', 'UBIFS', 'UFS', 'VXFS', 'WAFL', 'XIAFS', 'XFS', 'XSAN', 'ZFS', 'FUSEBLK'
];

appConfig.cfg_dir = appConfig.cfg_dir.replace(/^~/, os.homedir());

// log.info(`${appConfig.cfg_dir}`);

log.info('Config file at: ', path.join(__dirname, 'config.js'));

if (!fs.existsSync(appConfig.cfg_dir)) {
    fs.mkdirSync(appConfig.cfg_dir, {recursive: true});
}
const uuidjs = path.join(tapdata_work_dir,'uuid.js');
if (fs.existsSync(uuidjs)) {//`${appConfig.cfg_dir}/uuid.js`
    // 1. 从缓存文件加载
    appConfig.reportData.process_id = require(uuidjs);//`${appConfig.cfg_dir}/uuid.js`
} else {
    // 2. 没有缓存的，生成缓存，并赋值
    let uuid = uuidv4();
    appConfig.reportData.process_id = uuid; //注：process_id不是pid，命名不好，其实就是UUID
    //let uuid2f = `module.exports = '${uuid}';\n`;
    let uuid2f = 'module.exports = "'+uuid+'";\n';
    fs.writeFileSync(uuidjs, uuid2f);//`${appConfig.cfg_dir}/uuid.js`
}

log.info('Current active config is: \n', appConfig);

class Main {
    constructor() {//props
        /**
         * 需要采集的硬件信息
         */
        this.hwInfo = {
            cpu: {
                usage: 0,
                count: 0
            },
            mem: {
                total: 0, //--内存总大小KB
                used: 0, //--使用大小KB
                free: 0, //--剩余大小KB
            },
            networks: [],
            disk: []
        };

        /**
         * 采集器的timer id
         */
        this.timerIdOfCollector = null;


        /**
         * 配置文件变化监听器，由timer实现
         *
         */
        this.configMonitor = null;

        this.workerStatus = {
            worker_process_id: null,
            worker_process_start_time: null,
            worker_process_end_time: null,
            status: 'stop',
            exit_code: null
        }
    }

    /**
     * 启动
     */
    start() {

        Object.assign(this.workerStatus, {
            worker_process_id: '',
            worker_process_start_time: new Date().getTime(),
            status: 'starting'
        });
        report.setStatus({
            worker_status: this.workerStatus
        });

        //启动采集timer
        this.timerIdOfCollector = setInterval(this.gatherOsInfo, appConfig.gatherIntervals, this);

        // 启动 app 工作进程
        //this.startApp();

        // 监听配置文件变化
        this.startConfigChangeMonitor();
    }

    async gatherOsInfo(mthis) {
        //采集
        log.debug('gatherOsInfo============================');

        let fsSize = await si.fsSize();
        let disks = [];
        if (fsSize) {
            fsSize.forEach(data => {
                if (data.type && fs_types.includes(data.type.toUpperCase())) {
                    disks.push(
                        {
                            "totalGb": (data.size / 1024 / 1024 / 1024).toFixed(2),
                            "usedGb": (data.used / 1024 / 1024 / 1024).toFixed(2),
                            "freeGb": ((data.size - data.used) / 1024 / 1024 / 1024).toFixed(2),
                            "usedPercentage": data.use,
                            "freePercentage": 100 - data.use,
                            "fs": data.fs,
                            "mount": data.mount,
                            "fsType": data.type.toUpperCase()
                        }
                    );
                }
            });
            mthis.hwInfo.disk = disks;
        }

        let currentLoad = await si.currentLoad();
        if (currentLoad) {
            mthis.hwInfo.cpu.usage = currentLoad.currentload_user.toFixed(2);
            mthis.hwInfo.cpu.count = currentLoad.cpus.length;
        }

        let mems = await si.mem();
        mthis.hwInfo.mem.total = (mems.total / 1024).toFixed(2);
        mthis.hwInfo.mem.free = (mems.available / 1024).toFixed(2);
        mthis.hwInfo.mem.used = mthis.hwInfo.mem.total - mthis.hwInfo.mem.free;

        let netstats = await si.networkStats('*');
        let network_stats = [];
        await netstats.forEach(netstat => {
            if (netstat['operstate'] && netstat['operstate'] === 'up') {
                network_stats.push(netstat);
            }
        });
        mthis.hwInfo.networks = network_stats;

        log.debug('gather system data: ', mthis.hwInfo);

        // //上报
        report.setStatus({info: mthis.hwInfo});
    }

    /**
     * 停止
     */
    stop() {

        if (this.configMonitor) {
            this.configMonitor.stop();
            log.info('configMonitor process exited.');
        }
    }

    /**
     * 配置文件发生变化
     */
    startConfigChangeMonitor() {

        this.configMonitor = require('./monitor');

        this.configMonitor.on('message', (event) => {
            if (event && event.type === 'changed') {

                log.info('config is changed ...');

                const config = event.data;

                //无论什么变化，我都认为是采集间隔时间变化了，接下来：
                //重启timer，采集上报

                log.debug("what's this?", config);
                let newInterval = config.value * 1000;

                if (Number.isNaN(newInterval) || newInterval < 0) {
                    log.warn("Tapdata give me a invalid garther intervals, discard, give up resetting the timer (ms): ", appConfig.gatherIntervals);

                } else {
                    if (newInterval > 50 * 1000) {
                        //高于50s时，发送频率=50s
                        appConfig.reportIntervals = 50 * 1000;

                    } else {
                        //采集频率设置低于50s时，上报频率=采集频率
                        appConfig.reportIntervals = newInterval;
                    }
                    log.info("Reset garther and report timer...");
                    clearInterval(this.timerIdOfCollector);
                    log.info("old appConfig.gatherIntervals:", appConfig.gatherIntervals);
                    appConfig.gatherIntervals = newInterval;
                    log.info("new appConfig.gatherIntervals:", appConfig.gatherIntervals);
                    this.timerIdOfCollector = setInterval(this.gatherOsInfo, appConfig.gatherIntervals, this);

                    report.resetTimer();

                }

            }
        });

        this.configMonitor.start();
    }

}

const main = new Main();
main.start();

const exitHandler = function () {
    log.info("Stoping api gateway...");
    main.stop();
    log.info("api gateway stoped.");
};
process.on('exit', exitHandler);
//process.on('SIGKILL', exitHandler);
fs.writeFileSync(`${appConfig.cfg_dir}/server.pid`, `${process.pid}\n`, {encoding: 'utf-8'});
