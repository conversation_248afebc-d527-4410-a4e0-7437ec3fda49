let net = require('net');
let path = require('path');
const agent = require('./process/agent');
const readline = require('readline');
const basic = require('./basic');
const logger = require('./log/log');
//const conf = require("./conf")();
/*
if(conf._windows){
    pipe = path.join('\\\\?\\pipe',pipe);
}
 */
module.exports = function(msg,conf) {
    let enableDebug = (msg||'').indexOf('-debug') >= 0
    let client = new net.Socket();
    client.setEncoding('binary');
    let pipe = path.join(conf.WORK_DIR,'tapdataDir');//path.dirname(process.execPath)
    if(conf._windows){
        pipe = path.join('\\\\?\\pipe',pipe);
    }
    if (enableDebug) {
        console.log('Connect god process: ' + pipe)
    }
    client.connect(pipe, function () {
    });
    client.on('connect',()=>{
        if (enableDebug) {
            console.log('Send command into god process: ' + msg)
        }
        client.write(msg);
    });
    client.on('data', function (data) {//data
        let isClosed = false;
        if (data.indexOf('closeConn')>=0) {
            client.end();
            isClosed = true;
        }
        data = data.replace('closeConn','');
        if(data.includes("Downloading ")){
            readline.clearLine(process.stdout, 0);
            readline.moveCursor(process.stdout, 0,-1);
        }
        if(data.includes("<<<< ")){
            readline.clearLine(process.stdout, 0);
            readline.moveCursor(process.stdout, 0,-1);
            data = data.replace('<<<< ','');
        }
        console.log(data);
        if(isClosed && conf._windows && conf.showBackendCheck !== false){
            basic.exit().then();
        }
    });
    client.on('close', function () {
        //console.log('Connection closed ');
    });
    client.on('error', function (error) {
        //logger.error(error);
        console.log('Restart TapdataAgent ...:');
        agent.stopAPI(conf,['stop','agent']).then((res)=>{
            if(!res){
                console.info('Stop agent error,plases check your permitted.')
                return
            }
            console.log('TapdataAgent starting ...:');
            const checkGod = require('./checkGod');
            const arguments = process.argv.splice(2);
            let updateStatus = '';
            for(let i=0;i<arguments.length;i++) {
                if (['done', 'fail'].includes(arguments[i])) {
                    updateStatus = arguments[i]
                }
            }
            checkGod(conf,updateStatus).then(()=>{
                setTimeout(()=>{
                    const manage = require('./process/manage');
                    manage(JSON.parse(msg), conf);
                },5000)
            }).catch((e)=>{
                console.info(e)
            });
        }).catch((e)=>{
            console.error(e)
        })
        client.destroy();
    });
};