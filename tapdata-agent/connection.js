const WebSocket = require('ws');
const hostname = require('os').hostname();
let _platform = require('os').platform();
const basic = require('./basic');
const path = require('path');
const fs = require("fs");
const sign = require("./util/AKSK").sign;
const logger = require('./log/log');
let ws;
let reportIntervalId;
let reportInterval = 20000;
let ip;
let ips;
let urls;
let server;
let runningStatus = {
    engine:false,
    apiServer:false,
    management:false
};
let customMonitor = [];
let updateStatus = '';
const YAML = require('yamljs');
const {sendBehavior} = require("./util/sendBehavior");
module.exports = function (arguments) {
    for(let i=0;i<arguments.length;i++) {
        if (['done', 'fail'].includes(arguments[i])) {
            updateStatus = arguments[i]
        }
    }
    ips = getIPAdress();
    ip = ips.length > 0 ? ips[0] : "";
    const conf = require("./conf")(undefined, arguments);
    reportInterval = conf.reportInterval;
    if(reportInterval<20000)reportInterval = 20000;
    CLIConn(arguments, conf);
    ManagementConn(conf);
    report(conf);
};

function getIPAdress() {
    let interfaces = require('os').networkInterfaces();
    let ips = [];
    for (let devName in interfaces) {
        let iface = interfaces[devName];
        for (let i = 0; i < iface.length; i++) {
            let alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                ips.push(alias.address);
                //return alias.address;
            }
        }
    }
    return ips;
}

function CLIConn(arguments, conf) {
    const net = require('net');
    //const conf = require("./conf")(undefined,arguments[0]);
    let path = require('path');
    const readline = require('readline');
    const fs = require("fs");
    //const tapdataManagement = require('./process/tapdataManagement');
    //const backend = require('./process/backend');
    //const apiserver = require('./process/apiserver');
    //const getProcesses = require("getprocesses");
    //const basic = require('./basic');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    let pipe = path.join(conf.WORK_DIR, 'tapdataDir');//process.cwd() //path.dirname(process.execPath)
    if (conf._windows) {
        pipe = path.join('\\\\?\\pipe', pipe);
    }
    try {
        fs.unlinkSync(pipe);
    } catch (e) {
        console.info(e);
    }
    //console.info('conn...');
    //console.info(pipe);
    server = net.createServer({keepAlive:true},function (connect) {
        console.log('\r\ncli socket connect');
        connect.setEncoding('binary');
        connect.on('error', function (exception) {
            console.log('cli socket error:' + exception);
            connect.end();
            //server.close();
        });
        //客户端关闭事件
        connect.on('close', function (data) {
            console.log('cli socket closed: ',data);
        });
        connect.on("data", function (data) {//server接受到client发送的数据
            (async () => {
                console.log('Receive data from cli: ' + data);
                //server给client发送数据
                //console.info(data);
                let CLIConnect = {
                    'write':function(msg){//info
                        try {
                            console.log('Write data into cli: ' + msg)
                            connect.write(msg);
                        }catch(e){
                            logger.error(e);
                        }
                    }
                };
                data = JSON.parse(data);
                const conf = require("./conf")(undefined, data);
                await processesManagement(data, conf, CLIConnect);
                console.info('send close command into cli');
                connect.write('closeConn');
            })();
        })
    }).listen(
        pipe
    );//'\\getAppListDesktop' path.join('\\\\?\\pipe',process.cwd())
    server.on("error", function (exception) {
        console.error("server error:" + exception);
        //server.close();
    });
    server.on("close", function (exception) {
        console.error("server close:" + exception);
        //server.close();
    });
    process.on('exit', function () {
        logger.error("server exit.");
        console.log('Bye.');
        server.close();
    });
    rl.on('SIGCONT', () => {
        console.error("SIGCONT");
        server.close();
        process.exit();
    });
    process.on('SIGTERM', function () {
        console.error("SIGTERM");
        server.close();
    });
}

function ManagementConn(conf) {
    let backend_url = conf.backend_url;//"http://127.0.0.1:3000/api/,http://***********:3000/api/";//
    urls = backend_url.split(',');
    for (let i = 0; i < urls.length; i++) {
        if (urls[i].indexOf('https://') > -1){
            urls[i] = 'wss://' + urls[i].split('/api')[0].replace('https://', '');
        }
        else {
            urls[i] = 'ws://' + urls[i].split('/api')[0].replace('http://', '');
        }
        /*
        if(urls[i].indexOf('cloud.tapdata.net') > -1){
            urls[i] = urls[i].replace('/console', '');
        }
         */
    }
    conn(conf);
}
/**
 * @return {number}
 */
function GetRandomNum(Range) {
    let Rand = Math.random();
    return (Math.round(Rand * Range));
}

function conn(conf) {
    let url = urls[GetRandomNum(urls.length - 1)];
    if(conf.accessKey && conf.accessKey !== '') {
        url = url.replace('/tm_xdfwdsax','/tm');
        const u = url+'/ws/cluster/?access_token=PM51PWywjurdBe8KwXNpUkbB4yceUhOGwyREly6AmxF0Pr2b1qOynCdX06CTpk3G';
        url = sign(conf.accessKey, conf.secretKey, u);
    }else{
        url = url+'/ws/cluster/?access_token=PM51PWywjurdBe8KwXNpUkbB4yceUhOGwyREly6AmxF0Pr2b1qOynCdX06CTpk3G';
    }
    //console.info(url);
    if(url.indexOf('wss://') > -1){
        ws = new WebSocket(url, [], {handshakeTimeout: 2000,rejectUnauthorized: false});
    }else {
        ws = new WebSocket(url, [], {handshakeTimeout: 2000});//'ws://localhost:3000',[],{handshakeTimeout:500}
    }
    ws.on('open', function open() {
        // updateStatus
        if (updateStatus !== '') {
            try {
                basic.handleSendMsg(ws, 'updateMsg', {process_id: conf.process_id, status: updateStatus,msg: updateStatus});
                updateStatus = '';
            }catch(e){}
        }
        // ws.send('Hi Server');
    });
    ws.on('message', msg => {
        logger.info('receive ws message:',msg.toString());
        try {
            let data = JSON.parse(msg);
            ManagementCtrl(data, conf);
        } catch (e) {
            console.info('Parse message failed: ' + msg, e);
        }
        //updateReportInterval(conf, msg);
        //reportInterval = msg;
        //resetReport(conf);
    });
    ws.on('error', (e) => {
        console.info('Connect server error: ' + e.toString() + ', url: ' + url);
        //conn();
    });
    ws.on('close', () => {
        //ws = null;
        //console.info('WS is Closed');
        //conn(conf);
        setTimeout(function(){conn(conf);},2000);
        /*
        let start = (new Date()).getTime();
        while((new Date()).getTime() - start < 2000) {
        }
        conn(conf);
*/
    });
}
let statusB = {};
let process_id;
function report(conf) {
    if(conf._docker){
        _platform += '_docker';
    }
    const basic = require("./basic");
    process_id = conf.process_id;
    const cpus = require('os').cpus().length
    const totalmem = require('os').totalmem()
    reportIntervalId = setInterval(() => {
        (async () => {
            try {
                if(!process_id || process_id === ''){
                    let agentYML = path.join(conf.WORK_DIR,'agent.yml')
                    if(fs.existsSync(agentYML)){
                        let agentId = fs.readFileSync(agentYML,'utf8').toString();
                        if(agentId){
                            agentId = agentId.split(':');
                            if(agentId.length>1){
                                agentId = agentId[1];
                                process_id = agentId.replace('}','').trim();
                            }
                        }
                    }
                }
                //console.info('process_id:',process_id)
                const status = await basic.getStatus(conf,customMonitor);
                const now = (new Date()).getTime();
                let netStat = await basic.getNetStat(status.backendPid);
                let obj = {
                    'type': 'statusInfo',
                    'timestamp': now,
                    'data': {
                        'systemInfo': {
                            'hostname': hostname,
                            'uuid': conf.uuid,
                            'ip': ip,
                            'ips': ips,
                            'time': now,
                            'accessCode':conf.cloud_accessCode,
                            'username':conf.cloud_username,
                            'process_id':process_id,
                            'cpus':cpus,
                            'totalmem':totalmem,
                            'installationDirectory':conf.TAPDATA_HOME === "" ? "/" : conf.TAPDATA_HOME,
                            'work_dir':conf.WORK_DIR,
                            'os':_platform
                        },
                        'reportInterval': reportInterval,
                        'engine': {
                            'processID': status.backendPid,
                            'status': '' !== status.backendPid ? 'running' : 'stopped',
                            'netStat': netStat
                        },
                        'management': {
                            'processID': status.frontMPid,
                            'status': '' !== status.frontMPid ? 'running' : 'stopped'
                        },
                        'apiServer': {
                            'processID': status.apiMPid,
                            'status': '' !== status.apiMPid ? 'running' : 'stopped'
                        },
                        'mongodb': {
                            'processID': status.mongodbPid,
                            'status': '' !== status.mongodbPid ? 'running' : 'stopped'
                        },
                        'customMonitorStatus':customMonitor
                    }
                };
                if(statusB.engine && statusB.engine === 'running' && obj.data.engine.status === 'stopped'){
                    let msgObj = {
                        level:"warn",
                        system:"agent",
                        msg:"SYNCSeverStoppedSuccessfully",
                        title:"SYNCSeverStoppedSuccessfully",
                        serverName:"",
                        sourceId:conf.uuid
                    };
                    sandMsg('postMessage',msgObj);
                }
                if(statusB.management && statusB.management === 'running' && obj.data.management.status === 'stopped'){
                    let msgObj = {
                        level:"warn",
                        system:"agent",
                        msg:"manageSeverStoppedSuccessfully",
                        title:"manageSeverStoppedSuccessfully",
                        serverName:"",
                        sourceId:conf.uuid
                    };
                    sandMsg('postMessage',msgObj);
                }
                if(statusB.apiServer && statusB.apiServer === 'running' && obj.data.apiServer.status === 'stopped'){
                    let msgObj = {
                        level:"warn",
                        system:"agent",
                        msg:"APISeverStoppedSuccessfully",
                        title:"APISeverStoppedSuccessfully",
                        serverName:"",
                        sourceId:conf.uuid
                    };
                    sandMsg('postMessage',msgObj);
                }
                statusB.engine = obj.data.engine.status;
                statusB.management = obj.data.management.status;
                statusB.apiServer = obj.data.apiServer.status;
                console.info(JSON.stringify(obj))
                obj.sign = signString(JSON.stringify(obj));
                ws.send(JSON.stringify(obj));
                if(conf.appType === 'DFS'){
                    if(obj.data.engine.status === 'running' && !global.__backendStatus){
                        console.info('Set backend status to running');
                        global.__backendStatus = 'running';
                    }
                    if(global.__backendStatus === 'running' && obj.data.engine.status === 'stopped'){
                        console.info('starting backend....');
                        await processesManagement(['start', 'backend'], conf, {
                            write: (msg) => {
                                console.info(msg);
                            }
                        });
                    }
                }
            } catch (err) {
                console.info(err);
            }
        })();
    }, reportInterval);
}

function resetReport(conf) {
    clearInterval(reportIntervalId);
    report(conf);
}

function signString(str) {
    return basic.md5('tapdata' + str + '20200202');
}

function ManagementCtrl(data, conf) {
    //const logger = require('./log/log');
    let sign = data.sign;
    delete data.sign;
    if (!checkSign(sign, data.timestamp, JSON.stringify(data))) return;
    if ('changeStatus' === data.type) {
        //console.info('changeStatus');
        changeStatus(data, conf);
    }else if('customMonitor' === data.type){
        //console.info('getCustomMonitor');
        customMonitor = data['data']['customMonitor'];
    }else if('changeInterval' === data.type){
        //updateReportInterval(conf, data.data.time);
        //reportInterval = data.data.time;
        //resetReport(conf);
    }else if('getLogs' === data.type){
        console.info('getLogs Msg');
        console.info(data);
        const readLogs = require("./readLogs");
        const systemInfo = {'hostname':hostname,'ip':ip,'uuid':conf.uuid};
        readLogs(data.data.server,new Date(data.data.logsTime).getTime(),conf,systemInfo,function(msgArr){
            const now = (new Date()).getTime();
            let obj = {
                'type': 'logs',
                'timestamp': now,
                'data': msgArr
            };
            obj.sign = signString(JSON.stringify(obj));
            ws.send(JSON.stringify(obj));
        },function(msg){
            const now = (new Date()).getTime();
            let obj = {
                'type': 'logsFinished',
                'timestamp': now,
                'data': msg
            };
            obj.sign = signString(JSON.stringify(obj));
            ws.send(JSON.stringify(obj));
        });
    }else if('update' === data.type){
        //发送开始升级
        logger.info('begin user update.')
        sendBehavior(conf,{code:'beginUpdate',result:'successed',msg:''});
        update(conf, data.data, ws).then((res)=> {
            logger.info('download res:', res)
            if (!res) {
                //send error
                basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'fail'});
                return
            }
            chengeFile(conf,data.data).then(() => {

            })
        }).catch((e)=>{
            logger.info(e)
        })
    }else if('uploadLog' === data.type){
        require('./util/uploadLog').uploadLog(data.data,conf,ws).then().catch((e)=>{
            console.error(e)
        });
    }
}

async function update(conf, data, ws) {
    //const logger = require('./log/log');
    basic.handleSendMsg(ws,'updateMsg',{id:data.id,process_id:conf.process_id,status:'downloading',msg:'begin downloading'});
    if(data.token && data.token !== '' && data.token !== 'null' && data.token !== null){
        const application = fs.readFileSync(conf.WORK_DIR + '/application.yml');
        let yamlObj = YAML.parse(application.toString());
        if(!yamlObj['tapdata'])yamlObj['tapdata'] = {};
        if(!yamlObj['tapdata']['cloud'])yamlObj['tapdata']['cloud'] = {};
        yamlObj['tapdata']['cloud']['token'] = data.token;
        let yamlStr = YAML.stringify(yamlObj, 5);
        fs.writeFileSync(conf.WORK_DIR + '/application.yml', yamlStr);
    }
    let updateDir = '';
    console.info(data.downloadUrl)
    let downloadUrl = data.downloadUrl;
    updateDir = downloadUrl.split('/')[downloadUrl.split('/').length-2];
    const download = require('./util/download').download;
    const downloadList = data.downloadList;// ['log4j2.yml'];//'tapdata-agent', 'tapdata', 'tapdata.exe',
    logger.info("download fields:",downloadList);
    try{
        let arr = fs.readdirSync(process.cwd());
        for (let x in arr) {
            if (/dfs-+[\s\S]*?$/.test(arr[x])) {
                fs.rmdirSync(arr[x],{recursive : true})
            }
        }
    }catch (e) {
        console.error(e)
    }
    await download(conf, downloadUrl,'.md5sum',updateDir, ws, conf.process_id, null, true);
    for (let i = 0; i < downloadList.length; i++) {
        if((conf._linux && downloadList[i] === 'tapdata.exe') || (!conf._linux && downloadList[i] === 'tapdata')){
            continue
        }
        let num;
        if(['tapdata.exe','tapdata'].includes(downloadList[i]))
            num = 1;
        if('tapdata-agent' === downloadList[i])
            num = 2;
        logger.info('download:',downloadList[i])
        basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'downloading',msg:' download:'+downloadList[i]+''});
        if (!await download(conf,downloadUrl, downloadList[i],updateDir, ws, conf.process_id, num)) {
            basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'fail',msg:'download:'+downloadList[i]+' fail'});
            logger.info('download:',downloadList[i],' fail')
            return false
        }
        basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'downloading',msg:'download:'+downloadList[i]+' ok'});
        logger.info('download ',downloadList[i]," finished.")
    }
    sendBehavior(conf,{code:'downloadAgent',result:'successed',msg:''});
    basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'downloading',msg:'\ndownload finished. '});
    logger.info('download finished')
    return true
}

async function chengeFile(conf,data) {
    logger.info('begin spawn process')
    let downloadUrl = data.downloadUrl;
    let updateDir = downloadUrl.split('/')[downloadUrl.split('/').length-2];
    basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'upgrading',msg:'upgrading'});
    let GodExe = '';
    let homeDir = conf.TAPDATA_HOME === '' ? '/' : conf.TAPDATA_HOME;
    if (conf._windows) {
        GodExe += path.join(homeDir, 'update.exe');
    } else if (conf._linux) {
        GodExe += path.join(homeDir, '.update');
    }
    let needDel = false;
    try {
        fs.accessSync(GodExe, fs.constants.F_OK);
        needDel = true;
    } catch (e) {
    }
    if(needDel){
        try {
            fs.unlinkSync(GodExe);
        } catch (e) {
            sendBehavior(conf,{code:'upgradeAgent',result:'failed',msg:e});
            logger.error(e)
            return false;
        }
    }
    try {
        let from = path.join(conf.TAPDATA_HOME, 'tapdata');
        if (conf._windows) {
            from += '.exe';
        }
        fs.copyFileSync(from, GodExe);
        let stdio = ['ignore', 'ignore', 'ignore'];
        if (conf._windows) {
            let subProcess1 = require('child_process').spawn('attrib', ['+h', GodExe], {
                'stdio': stdio,
                'detached': true,
                'windowsHide': true
            });
            subProcess1.unref();
        }
    } catch (e) {
        sendBehavior(conf,{code:'upgradeAgent',result:'failed',msg:e});
        logger.error(e)
        return false
    }
    basic.handleSendMsg(ws,'updateMsg',{process_id:conf.process_id,status:'upgrading',msg:'restart'});
    let checkBackStr;
    const homePaht = conf.TAPDATA_HOME === '' ? '/' : conf.TAPDATA_HOME;
    if (conf._linux) {
        checkBackStr = path.join(homePaht, ".update");
    }else{
        checkBackStr = path.join(homePaht, "update");
    }
    logger.info('spawn update process:',checkBackStr);
    let stdio = ['ignore','ignore','ignore'];
    let subProcess = require('child_process').spawn(checkBackStr, ['update','--workDir',conf.WORK_DIR,'--updateDir',updateDir], {//,'--workDir',conf.WORK_DIR
        'cwd':conf.TAPDATA_HOME,
        'stdio': stdio,
        //'env': env,
        'detached': true,
        'windowsHide':true
    });
    subProcess.unref();
    return true
}

function changeStatus(data, conf) {
    let connect = {};
    connect.write = function (msg,systemObj) {
        const now = (new Date()).getTime();
        let obj = {
            'type': 'responseLog',
            'timestamp': now,
            'data': {
                'msg': msg,
                'time':new Date(),
                'hostname':hostname,
                'ip':ip,
                'uuid':conf.uuid,
                'server':systemObj.server,
                'level':systemObj.level,
                'accessCode':conf.cloud_accessCode,
                'username':conf.cloud_username
            }
        };
        obj.sign = signString(JSON.stringify(obj));
        console.info(obj)
        ws.send(JSON.stringify(obj));
    };
    connect.write('Agent get instructions :'+data.data.operation + ' '+data.data.server ,{'server':'tapdataAgent','level':'INFO'});
    if(runningStatus[data.data.server]){
        connect.write('Agent is busy,please try again!',{'server':'tapdataAgent','level':'ERROR'});
        return;
    }
    runningStatus[data.data.server] = true;
    (async () => {
        try {
            const now = (new Date()).getTime();
            let obj = {
                'type': 'changeStatus',
                'timestamp': now,
                'data': {
                    '_id': data.data._id,
                    'server':data.data.server
                }
            };
            let server = data.data.server;
            if(server === 'engine')server = 'backend';
            if(server === 'management')server = 'frontend';
            if(server === 'apiServer')server = 'apiserver';
            let myData = [];
            myData.push(data.data.operation);
            myData.push(server);
            myData.push('-debug');
            //console.info(myData);
            let res = await processesManagement(myData, conf, connect);
            if (res)
                obj.data.status = 'true';
            else
                obj.data.status = 'false';
            obj.sign = signString(JSON.stringify(obj));
            ws.send(JSON.stringify(obj));
        } catch (e) {
            connect.write(e,{'server':'tapdataAgent','level':'WARN'});
        }
        runningStatus[data.data.server] = false;
    })();
}

function updateReportInterval(conf, thisReportInterval) {
    const fs = require("fs");
    const YAML = require('yamljs');
    const data = fs.readFileSync(conf.WORK_DIR + '/application.yml');
    let yamlObj = YAML.parse(data.toString());
    yamlObj['tapdata']['conf']['reportInterval'] = thisReportInterval;
    let yamlStr = YAML.stringify(yamlObj, 5);
    fs.writeFileSync(conf.WORK_DIR + '/application.yml', yamlStr);
    reportInterval = thisReportInterval;
    resetReport(conf);
}

function checkSign(sign, time, str) {
    if (basic.isEmpty(sign) || basic.isEmpty(time) || basic.isEmpty(str)) return false;
    str = 'tapdata' + str + '20200202';
    const now = new Date().getTime();
    if (time + 1000*60*10 < now)return false;
    return basic.md5(str) === sign;
}

module.exports.processesManagement = processesManagement = async function (data, conf, connect) {
    const tapdataManagement = require('./process/tapdataManagement');
    const backend = require('./process/backend');
    const apiserver = require('./process/apiserver');
    const mongodb = require('./process/mongodb');
    const getProcesses = require("getprocesses");
    const basic = require('./basic');
    //const logger = require('./log/log');
    if (data[0] === 'start') {
        let list;// = await getProcesses.getProcesses();
        if (conf._windows) {
            list = await require("./ps_win").getProcessesWindows();
        }else {
            list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        }
        let frontHavePid = false;
        let backendHavePid = false;
        let apiHavePid = false;
        let mongodbHavePid = false;
        let checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/index.js";
        let checkFrontStrN = conf.COMPONENTS_DIR + "/tm.jar";
        let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent";
        let checkMApiSrt = conf.COMPONENTS_DIR + "/apiserver/index";
        let checkMongoDBStr = conf.COMPONENTS_DIR + "/mongod";
        if (conf._windows) {
            checkFrontStr = checkFrontStr.replace(/\//g, '\\');
            checkBackStr = checkBackStr.replace(/\//g, '\\');
            checkMApiSrt = checkMApiSrt.replace(/\//g, '\\');
            checkMongoDBStr = checkMongoDBStr.replace(/\//g, '\\');
        }
        for (let i = 0; i < list.length; i++) {
            if (list[i].arguments.join(' ').indexOf(checkFrontStr) >= 0 || list[i].arguments.join(' ').includes(checkFrontStrN)) {
                frontHavePid = true;
                if (basic.isDebug(data)) {
                    //console.info("frontend is started.We are not need start frontend.");
                }
            }
            if (list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {
                backendHavePid = true;
                if (basic.isDebug(data)) {
                    //console.info("backend is started.We are not need start backend.");
                }
            }
            if (list[i].arguments.join(' ').indexOf(checkMApiSrt) >= 0) {
                apiHavePid = true;
                if (basic.isDebug(data)) {
                    //console.info("apiserver is started.We are not need start apiserver.");
                }
            }
            if ((list[i].command + ' ' + list[i].arguments.join(' ')).indexOf(checkMongoDBStr) >= 0) {
                mongodbHavePid = true;
                if (basic.isDebug(data)) {
                    //console.info("mongodb is started. We are not need start mongodb.");
                }
            }
        }
        if (data[1] === 'frontend') {
            if(frontHavePid){
                let reportInfo = {'server':'management','level':'WARN'};
                connect.write("Frontend is already started.Skipping start operation.",reportInfo);
                return false;
            }
            try {
                await tapdataManagement.startFrontend(conf, data, connect);
            } catch (e) {
                let reportInfo = {'server':'management','level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'backend') {
            if(backendHavePid){
                let reportInfo = {'server':'engine','level':'WARN'};
                connect.write("Backend is already started.Skipping start operation.",reportInfo);
                return false;
            }
            try {
                await backend.startBackend(conf, data, connect);
            } catch (e) {
                let reportInfo = {'server':'engine','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'apiserver') {
            if(apiHavePid){
                let reportInfo = {'server':'apiServer','level':'WARN'};
                connect.write("ApiServer is already started.Skipping start operation.",reportInfo);
                return false;
            }
            try {
                await apiserver.startApi(conf, data, connect);
            } catch (e) {
                let reportInfo = {'server':'apiServer','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === mongodb.MONGODB_PROCESS_NAME) {
            if (mongodbHavePid) {
                let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'WARN'};
                connect.write("MongoDB is already started. Skipping start operation.", reportInfo);
                return false;
            }
            try {
                await mongodb.startMongoDB(conf, data, connect);
            } catch (e) {
                let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                console.error(e)
                return false;
            }
            return true;
        } if (data[1] === 'deployConnector') {
            const deployConnector = require('./process/deployConnector')
            deployConnector.deployConnector(conf, data, connect)
            return true;
        } else {
            if (!mongodbHavePid) {
                try {
                    let result = await mongodb.startMongoDB(conf, data, connect)
                    if (!result) return false;
                } catch (e) {
                    let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'ERROR'};
                    connect.write(e.toString(),reportInfo);
                    return false;
                }
            }
            if (!frontHavePid){
                try{
                    await tapdataManagement.startFrontend(conf, data, connect);
                }catch(e) {
                    return false;
                }
            }
            try {
                if (!backendHavePid)
                    await backend.startBackend(conf, data, connect);
            } catch (e) {
                console.error(e)
                connect.write('Start Backend failed: ' + e.toString());
            }
            try {
                if (!apiHavePid)
                    await apiserver.startApi(conf, data, connect);
            } catch (e) {
                console.error(e)
                connect.write('Start APIServer failed: ' + e.toString());
            }
        }
    } else if (data[0] === 'stop') {
        if (data[1] === 'frontend') {
            try {
                await tapdataManagement.stopFrontend(conf, false, connect);
            } catch (e) {
                let reportInfo = {'server':'management','level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'backend') {
            try {
                await backend.stopBackend(conf, false, connect);
            } catch (e) {
                let reportInfo = {'server':'engine','level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'apiserver') {
            try {
                await apiserver.stopAPI(conf, false, connect);
            } catch (e) {
                let reportInfo = {'server':'apiServer','level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === mongodb.MONGODB_PROCESS_NAME) {
            try {
                await mongodb.stopMongoDB(conf, false, connect);
            } catch (e) {
                let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'ERROR'};
                connect.write(e.toString(),reportInfo);
                return false;
            }
        } else if (data[1] === 'deployConnector') {
            const deployConnector = require('./process/deployConnector')
            deployConnector.stopDeploy(conf, connect)
        } else if (data[1] === 'agent') {
            await connect.write('Bye');
            server.close();
            process.exit();
        } else {
            if(conf.appType === 'DFS'){
                try {
                    await backend.stopBackend(conf, false, connect);
                } catch (e) {
                    console.error(e)
                    connect.write('Stop Backend failed: ' + e.toString());
                }
            }else {
                try {
                    const deployConnector = require('./process/deployConnector')
                    deployConnector.stopDeploy(conf, connect)
                } catch (e) {
                    console.error(e)
                    connect.write('Stop deploy connector worker failed: ' + e.toString());
                }
                try {
                    await tapdataManagement.stopFrontend(conf, false, connect);
                } catch (e) {
                    console.error(e)
                    connect.write('Stop Frontend failed: ' + e.toString());
                }
                try {
                    await backend.stopBackend(conf, false, connect);
                } catch (e) {
                    console.error(e)
                    connect.write('Stop Backend failed: ' + e.toString());
                }
                try {
                    await apiserver.stopAPI(conf, false, connect);
                } catch (e) {
                    console.error(e)
                    connect.write('Stop APIServer failed: ' + e.toString());
                }
                try {
                    await mongodb.stopMongoDB(conf, false, connect);
                } catch (e) {
                    let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'ERROR'};
                    connect.write(e.toString(),reportInfo);
                }
            }
            connect.write('Bye');
            server.close();
            process.exit();
        }
    } else if (data[0] === 'restart') {
        if (data[1] === 'frontend') {
            try {
                await tapdataManagement.stopFrontend(conf, true, connect);
            } catch (e) {
                let reportInfo = {'server':'management','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'backend') {
            try {
                await backend.stopBackend(conf, true, connect);
            } catch (e) {
                let reportInfo = {'server':'engine','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === 'apiserver') {
            try {
                await apiserver.stopAPI(conf, true, connect);
            } catch (e) {
                let reportInfo = {'server':'apiServer','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else if (data[1] === mongodb.MONGODB_PROCESS_NAME) {
            try {
                await mongodb.stopMongoDB(conf, true, connect);
            } catch (e) {
                let reportInfo = {'server':mongodb.MONGODB_PROCESS_NAME,'level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        } else {
            try {
                if(conf.appType === 'DFS'){
                    try {
                        await backend.stopBackend(conf, true, connect);
                    } catch (e) {
                        console.error(e)
                        connect.write('Restart Backend failed: ' + e.toString());
                    }
                }else {
                    try {
                        await tapdataManagement.stopFrontend(conf, true, connect);
                    } catch (e) {
                        console.error(e)
                        connect.write('Restart Frontend failed: ' + e.toString());
                    }
                    try {
                        await backend.stopBackend(conf, true, connect);
                    } catch (e) {
                        console.error(e)
                        connect.write('Restart Backend failed: ' + e.toString());
                    }
                    try {
                        await apiserver.stopAPI(conf, true, connect);
                    } catch (e) {
                        console.error(e)
                        connect.write('Restart APIServer failed: ' + e.toString());
                    }
                    //await mongodb.stopMongoDB(conf, true, connect);
                }
            } catch (e) {
                let reportInfo = {'server':'restart','level':'ERROR'};
                connect.write(e,reportInfo);
                return false;
            }
            return true;
        }
    }else if(data[0] === 'update'){
        //发送开始升级
        const updateObj = {
            "id":"",
            "downloadUrl":"",
            "downloadList": ["tapdata", "tapdata.exe", "tapdata-agent", "log4j2.yml"]
        }
        if(data.length>2 && data[1] === 'downloadUrl' && data[2]){
            updateObj.downloadUrl = data[2]
        }else{
            connect.write('update error',reportInfo);
            return
        }
        logger.info('begin update.')
        connect.write('begin update');
        let res = await update(conf, updateObj, connect)
        logger.info('download res:', res)
        if (!res) {
            //send error
            logger.error('update fail');
            basic.handleSendMsg(connect, 'updateMsg', {process_id: conf.process_id, status: 'fail'});
            return
        }
        connect.write('');
        connect.write('restart service.');
        await chengeFile(conf, updateObj)
    }else if(data[0] === 'register'){
        if(data.length > 1){
            let files = [data[1]];
            try{
                for(let x in files){
                    if(!fs.existsSync(files[x])){
                        connect.write('File is not exists.');
                        return;
                    }
                }
            }catch (e) {
                connect.write(e.toString());
                return;
            }
            try {
                const RandomNum = GetRandomNum(urls.length - 1);
                let bUrl = conf.backend_url.split(',')[RandomNum].replace('/api/','').replace('/api','');
                await basic.deploy(conf,connect,{},bUrl,files,false);
            }catch (e) {
                console.info(e)
            }finally {
            }
        }else{
            connect.write('File path cannot be empty.');
        }
    } else if (data[0] === 'status') {
        const {deployStatus} = require('./process/deployConnector')
        deployStatus(conf, connect)
        return true;
    }
}

module.exports.sandMsg = sandMsg = function (type,msg) {
    if(!ws){
        return;
    }
    try {
        const now = (new Date()).getTime();
        msg.serverName = ip;
        let obj = {
            'type': type,
            'timestamp': now,
            'data': msg
        };
        obj.sign = signString(JSON.stringify(obj));
        ws.send(JSON.stringify(obj));
    }catch(e){
        console.info(e);
    }
};
