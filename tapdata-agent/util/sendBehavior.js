const http = require("http");
const https = require("https");
const os = require("os");
const arch = os.arch();
const osType = os.type();
const platform = os.platform();
const basic = require('../basic');
let urls = [];
exports.sendBehavior = function(conf,data){
    if(conf.isCloud){
        let sid = "/instance_" + process.systemStartTime;
        let where = {
            "attrs.sid": sid
        }
        let d = {
            "accessToken": conf.cloud_token,
            "ak": conf.accessKey,   // tcm 分配的access key,
            "process_id": conf.process_id, // 启动引擎时填写process_id
            "component": 'TapdataAgent',//engine/apiServer/agent
            "os": osType,
            "arch": arch,
            "platform": platform, // 硬件架构
            "result": data.result,//"successed/failed" // 本次启动成功还是失败,
            "msg": data.msg//失败时可以填写相关的失败原因
        }
        let backend_url = conf.backend_url;//"http://127.0.0.1:3000/api/,http://192.168.1.6:3000/api/";//
        if(urls.length === 0){
            urls = backend_url.split(',');
        }
        if(urls.length === 0){
            return;
        }
        let sendObj = {
            ts: new Date().getTime(),
            headers: {},
            userId: conf.cloud_username || conf.user_id, // 参与行为的用户，前端可以指定用户id，当用户登录控制台后，后台使用登录回话的用户ID
            page: "TapdataAgent",                        // 行为发生的页面，可以是 path 或者 uri
            code: data.code,                        // 行为代码
            attrs: d
        }
        let url = urls[0];//.split('/console')[0] + '/console';
        url = url.replace('/tm/','/');
        if(url.substring(url.length - 1) !== '/'){
            url += "/"
        }
        url += 'tcm/user/behavior?data=' + encodeURIComponent(JSON.stringify(sendObj)) + '&where=' + encodeURIComponent(JSON.stringify(where))
        let h = https;
        if(url.includes('http://')){
            h = http;
        }
        if(basic.isDebug(process.tapdataArguments)){
            console.info('send:',url);
        }
        h.get(url,(res)=>{
            if(basic.isDebug(process.tapdataArguments)) {
                console.log('statusCode:', res.statusCode);
                console.log('headers:', res.headers);
            }
            let data = '';
            res.on('data', (d) => {
                data += d;
            });
            res.on('end',()=>{
                if(basic.isDebug(process.tapdataArguments)) {
                    console.info(data.toString());
                }
            })
        })
    }
}

function GetRandomNum(Range) {
    let Rand = Math.random();
    return (Math.round(Rand * Range));
}