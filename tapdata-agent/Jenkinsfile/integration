pipeline {
    
    agent any
	
	options {
		buildDiscarder(logRotator(numToKeepStr: '5'))
        timeout(time: 1, unit: 'HOURS')
    }
	
	parameters {
        string(name: 'TESTSCRIPT', defaultValue: 'test_landing_page.py', description: 'python test script')
        string(name: 'HOMEPAGE', defaultValue: 'http://*************:3030/', description: 'tapdata url')
        string(name: 'MIDDB', defaultValue: '*************:27017', description: 'Intermediate library')
	    string(name: 'PROFILE', defaultValue: 'DEFAULT', description: 'BUILD_PROFILE')
	}
	
    environment {
        PINPOINT='true'
	BUILD_PROFILE="${params.PROFILE}"
    }
		
    stages {
        stage('Apig Checkout') {
            steps {
                checkout([$class: 'GitSCM', branches: [[name: '*/tapdata-agent-branch']], doGenerateSubmoduleConfigurations: false, extensions: [[$class: 'CloneOption', depth: 0, noTags: false, reference: '', shallow: false, timeout: 300], [$class: 'RelativeTargetDirectory', relativeTargetDir: 'tapdataAgent']], submoduleCfg: [], userRemoteConfigs: [[credentialsId: '5eeccd05-7611-41b9-b293-7065bb7b3729', url: 'https://gitlab.com/develop13/tapdata-agent.git']]])
            }        
        }    
        stage('Build') {
            steps {

                sh '''export TapDataAgent=$(git describe --long HEAD)
                cd tapdataAgent
                rm -rf node_modules
                npm install
                pkg -c package.json -t node12-linux-64 -o ~/tapdata
            }
        }
    }
    post {
	failure {
			mail to: '<EMAIL>',
			subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
			body: "Something is wrong with ${env.BUILD_URL}"
        }
    }
}