const basic = require('../basic');
const path = require('path');
const moment = require('moment');
const fs = require('fs');
let reportInfo = {'server':'engine','level':'INFO'};
const {sendBehavior} = require("../util/sendBehavior");
const child_process = require("child_process");
const connectionSendMsg = require("../connection").sandMsg || function(){};
module.exports = {
    doStartBackend:function (conf,arguments) {//启动后台程序
        //msg(JSON.stringify(arguments));
        //startBackendFun(conf,arguments);
    },
    stopBackend:async function (conf,isRestart,connect) {
        //(async () => {
            let list;// = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        if (conf._windows) {
            list = await require("../ps_win").getProcessesWindows();
        }else {
            list = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        }
            let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent";
            checkBackStr = basic.getExecStr(conf,checkBackStr);
            let isKilled = false;
            let ticdcStr = path.join(conf.COMPONENTS_DIR,'ticdc');
            ticdcStr = basic.getExecStr(conf,ticdcStr);
            for (let i = 0; i < list.length; i++) {
                if (list[i].command.indexOf(checkBackStr) >= 0 || list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
                    basic.killPid(list[i].pid,conf);
                    isKilled = true;
                    let msgObj = {
                        process_id:conf.process_id
                    }
                    connectionSendMsg('updateWorkerPingTime',msgObj);
                }
                if (list[i].command.indexOf(ticdcStr) >= 0 || list[i].arguments.join(' ').indexOf(ticdcStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
                    basic.killPid(list[i].pid,conf);
                    connect.write("Ticdc service down.",reportInfo);
                }
            }
            if(isKilled && basic.isEmpty((await basic.getStatus(conf)).backendPid)) {
                sendBehavior(conf,{code:'stopBackend',result:'successed',msg:''});
                setTimeout(()=>{
                    connect.write("backend service down.",reportInfo);
                },1000)
                console.info("backend service down.");
                let msgObj = {
                    level:"warn",
                    system:"agent",
                    msg:"SYNCSeverStoppedSuccessfully",
                    title:"SYNCSeverStoppedSuccessfully",
                    serverName:"",
                    sourceId:conf.uuid
                }
                //connectionSendMsg('postMessage',msgObj);
            }else if(isKilled){
                await this.stopBackend(conf,isRestart,connect);
            }else if(!isKilled){
                connect.write('Backend is already stopped. Skipping stop operation.',reportInfo);
                //console.info('Backend is already stopped. Skipping stop operation.');
            }
        global.__backendStatus = 'stopped';
            if(isRestart)
                await this.startBackend(conf,arguments,connect);
        //})();
    },
    startBackend:async function(conf,arguments,connect){
        global.__backendStatus = 'starting';
        if( conf.TAPDATA_JAVA_OPTS && conf.TAPDATA_JAVA_OPTS.includes('-Xmx')){
            let mem = conf.TAPDATA_JAVA_OPTS.substring(conf.TAPDATA_JAVA_OPTS.indexOf('-Xmx')+4,conf.TAPDATA_JAVA_OPTS.length)
            mem = mem.split(' ')[0];
            connect.write("The max memory will be used by Tapdata is " + mem,reportInfo);
        }
        conf = require("../conf")(undefined, []);

        // Extract openapi-generator.tap file if exists
        try {
            const openapiGeneratorPath = path.join(conf.TAPDATA_HOME, 'etc', 'openapi-generator.tap');
            if (fs.existsSync(openapiGeneratorPath)) {
                console.info('Found openapi-generator.tap file, attempting to extract...');
                connect.write("Found openapi-generator.tap file, attempting to extract...", reportInfo);

                const extractPath = path.join(conf.TAPDATA_HOME, 'etc');
                let unzipCommand;

                if (conf._windows) {
                    // Use PowerShell Expand-Archive on Windows
                    unzipCommand = `powershell -Command "Expand-Archive -Path '${openapiGeneratorPath}' -DestinationPath '${extractPath}' -Force"`;
                } else {
                    // Use unzip command on Unix-like systems
                    unzipCommand = `unzip -o "${openapiGeneratorPath}" -d "${extractPath}"`;
                }

                await new Promise((resolve, reject) => {
                    child_process.exec(unzipCommand, { windowsHide: true }, (error, stdout, stderr) => {
                        if (error) {
                            console.warn('Warning: Failed to extract openapi-generator.tap:', error.message);
                            connect.write("Warning: Failed to extract openapi-generator.tap: " + error.message, reportInfo);
                            resolve(); // Continue execution even if extraction fails
                        } else {
                            console.info('Successfully extracted openapi-generator.tap');
                            connect.write("Successfully extracted openapi-generator.tap", reportInfo);
                            resolve();
                        }
                    });
                });
            } else {
                console.warn('Warning: openapi-generator.tap file not found in etc directory');
                connect.write("Warning: openapi-generator.tap file not found in etc directory", reportInfo);
            }
        } catch (error) {
            console.warn('Warning: Error processing openapi-generator.tap:', error.message);
            connect.write("Warning: Error processing openapi-generator.tap: " + error.message, reportInfo);
        }
        if(conf.isCloud !== true && conf.isCloud !== 'true'){
            let accesscode = await basic.getAccesscode(conf);
            if(accesscode && accesscode !== ''){
                conf.cloud_accessCode = accesscode;
            }
        }
        let env = process.env;
            env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
            env.TAPDATA_PORT = conf.tapdata_port;
            env.API_SERVER_PORT = conf.api_server_port;//'3080';
            env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
            env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
            env.NODE_HOME = conf.NODE_HOME;
            env.TAPDATA_HOME = conf.TAPDATA_HOME;
            env.ssl = conf.ssl;
            env.sslCA = conf.mongo_sslCAFile;
            env.sslCertKey = conf.mongo_sslPEMKeyFile;
            env.cloud_accessCode = conf.cloud_accessCode;
            env.cloud_retryTime = conf.cloud_retryTime;
            env.cloud_baseURLs = conf.cloud_baseURLs;
            env.mode = conf.mode;
            env.backend_url = conf.backend_url;
            env.MONGO_SSL = conf.ssl;
            env.MONGO_SSL_CA = conf.MONGO_SSL_CA;
            env.MONGO_SSL_CERT_KEY = conf.MONGO_SSL_CERT_KEY;
            env.MONGO_SSL_PASS = conf.mongo_sslPEMKeyFilePassword;
            env.isCloud = conf.isCloud || conf.backend_url.indexOf('cloud.tapdata.net') > -1;
            env.process_id = conf.process_id;
            env.jobTags = conf.jobTags;
            env.version = conf.version;
            env.user_id = conf.cloud_username;
            env.app_type = conf.appType;
            env.accessKey = conf.accessKey;
            env.secretKey = conf.secretKey;
            env.sslCAPath = conf.sslCAPath;
            env.sslCertKeyPath = conf.sslCertKeyPath;
        conf.TAPDATA_JAVA_OPTS = conf.TAPDATA_JAVA_OPTS.trim() ;//+ " -Dspring.config.location=file:"+conf.WORK_DIR+"/application.yml -Dlogging.config=file:"+conf.TAPDATA_HOME+"/etc/log4j2.yml ";
        let newArgs = []
        if(conf.TAPDATA_JAVA_OPTS && conf.TAPDATA_JAVA_OPTS !== "")
            newArgs = conf.TAPDATA_JAVA_OPTS.split(" ");
        newArgs.push('-XX:+HeapDumpOnOutOfMemoryError');
        let HeapDumpPath = "-XX:HeapDumpPath=" + path.join(conf.WORK_DIR,'oom-heap-') + moment().format('YYYYMMDDHH') + '.dmp';
        newArgs.push(HeapDumpPath);
        newArgs.push('-XX:-OmitStackTraceInFastThrow');
        let javaPath = "java";
        try{
            fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe'), fs.constants.F_OK);
            javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe');
        }catch (e) {
        }
        try{
            fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java'), fs.constants.F_OK);
            javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java');
        }catch (e) {
        }
        console.info("JAVA PATH:",javaPath);
        let javaVersion = await getJavaVersion(javaPath);
        if(javaVersion && javaVersion !== ''){
            //let JDKVersion = javaVersion.split("version")[1].split('"')[1].substring(0, 3);
            if ((javaVersion === "11.")) {
                newArgs.push('--add-exports');
                newArgs.push('jdk.naming.dns/com.sun.jndi.dns=java.naming');
            }
            if ((javaVersion.includes('17'))) {
                newArgs.push('--add-opens=java.base/java.lang=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/java.util=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/java.security=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/sun.security.rsa=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/sun.security.x509=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/sun.security.util=ALL-UNNAMED');
                newArgs.push('--add-opens=java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED');
                newArgs.push('-XX:+UnlockExperimentalVMOptions');
                newArgs.push('--add-exports=java.base/jdk.internal.ref=ALL-UNNAMED');
                newArgs.push('--add-exports=java.base/sun.nio.ch=ALL-UNNAMED');
                newArgs.push('--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED');
                newArgs.push('--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED');
                newArgs.push('--add-opens=jdk.compiler/com.sun.tools.javac=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/java.lang.reflect=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/java.io=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/java.util=ALL-UNNAMED');
                // Reflective access to java.time is required for compatibility with libraries or frameworks that use reflection to access date and time APIs.
                newArgs.push('--add-opens=java.base/java.time=ALL-UNNAMED');
                newArgs.push('--add-modules=java.se');
                newArgs.push('--add-opens=java.management/sun.management=ALL-UNNAMED');
                newArgs.push('--add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED');
                newArgs.push('--add-opens=java.base/jdk.internal.loader=ALL-UNNAMED');
            }
        }
        let execString = "-Dspring.config.location=file:"+conf.WORK_DIR+"/application.yml -Dlogging.config=file:"+conf.TAPDATA_HOME+"/etc/log4j2.yml " + "-jar "  + conf.COMPONENTS_DIR + "/" + conf.AGENT_JAR_FILE;//-Djava.security.egd=file:/dev/./urandom -Duser.timezone=GMT+08
        execString = basic.getExecStr(conf,execString);
        console.info('begin do start backend');
        console.info('is debug:'+basic.isDebug(arguments));
        let stdio = ['ignore','ignore','ignore'];//outLog
        if(basic.isDebug(arguments)){
            console.info("start backend env :");
            console.info(env);
            connect.write("start backend env :" + JSON.stringify(env),reportInfo);
            console.info("start backend args :");
            console.info(execString);
            connect.write("start backend args :" + execString,reportInfo);
            console.info("start backend cwd :");
            console.info(env.TAPDATA_HOME);
            connect.write("start backend cwd :" + env.TAPDATA_HOME,reportInfo);
            //stdio = ['inherit','inherit','inherit'];
        }
        let javaArgs = execString.trim().split("-D");
        for(let x in javaArgs){
            if(javaArgs[x].replace(" ","") === "")continue;
            let item = javaArgs[x].split("-jar");
            for (let i = 0; i < item.length; i++) {
                if (i < 1) {
                    newArgs.push("-D" + item[i].trim());
                }
                else {
                    let argStr = "-jar" + item[i].trim();
                    newArgs.push("-jar");
                    newArgs.push(item[i].trim());
                }
            }
        }
        basic.deleteBackendStatus(conf);
        env.TAPDATA_JAVA_PATH = javaPath;
        env.TAPDATA_BACKEND_ARGS = JSON.stringify(newArgs);
        let subprocess = require('./subprocess');
        try{
            await subprocess(conf,env);
        }catch (e) {
            console.error(e)
        }
        /*
        let subProcess = require('child_process').spawn(javaPath,newArgs, {
            'env': env,
            'cwd':env.TAPDATA_HOME,
            'stdio':stdio,
            'windowsHide': true,
            'detached': true
        });
        */
        /*
        const log4js = require("log4js");
        log4js.configure({
            appenders: { cheese: {compress: true, alwaysIncludePattern:true , daysToKeep: 7, type: "dateFile", filename: conf.WORK_DIR + "/logs/tapdata-agent/backendDebug.log" } },
            categories: { default: { appenders: ["cheese"], level: "error" } }
        });
        const logger = log4js.getLogger("cheese");
        subProcess.stderr.on('data',(msg)=>{
            logger.error(msg.toString());
        });
         */
        let res = false;
        let errorMsg = '';
        const now = new Date().getTime();
        res = true;

        console.info('begin check');
        let waitingMsg = "<<<< Waiting for the flow engine to start ";
        connect.write("Waiting for the flow engine to start",reportInfo)
        let waitingTimes = 1;
        while(true){
            const backendStatus = basic.readBackendStatus(conf)
            if(backendStatus){
                if(backendStatus.status === 'ok') {
                    res = true;
                    break
                }else{
                    res = false;
                    errorMsg = backendStatus.msg || '';
                    break
                }
            }
            if(new Date().getTime() - now > 60000){
                res = false;
                errorMsg = 'start timeout';
                console.info('time out res=false')
                break
            }
            await sleep(1000);
            waitingTimes++;
            let point = '';
            /*for(let z=0;z<waitingTimes % 3 ;z++){
                point = point + '.';
            }*/
            if(waitingTimes % 3 === 0 ){
                point = '-';
            }else if(waitingTimes % 3 === 1 ){
                point = '/';
            }else{
                point = "\\";
            }
            connect.write(waitingMsg+point,reportInfo);
        }

        global.__backendStatus = 'running';

        if(res) {
            console.info('check ok')
            //if(!basic.isEmpty((await basic.getStatus(conf)).backendPid)) {
            connect.write("FlowEngine started.", reportInfo);
            sendBehavior(conf,{code:'startBackend',result:'successed',msg:''});
            console.info("FlowEngine started.");
            let msgObj = {
                level: "info",
                system: "agent",
                msg: "SYNCSeverStartedSuccessfully",
                title: "SYNCSeverStartedSuccessfully",
                serverName: "",
                sourceId: conf.uuid
            };
            setTimeout(() => {
                connectionSendMsg('postMessage', msgObj);
            }, 2000);
        }else{
            console.info('check faile');
            connect.write("FlowEngine start fail.", reportInfo);
            connect.write("FlowEngine start error: "+errorMsg+".", reportInfo);
            await sleep(300);
            connect.write("log file at :" + path.join(conf.WORK_DIR,"logs",'agent',"tapdata-agent.log"), reportInfo);
            console.info("FlowEngine start fail.");
            sendBehavior(conf,{code:'startBackend',result:'fail',msg:'start time out'});
            //await this.stopBackend(conf,false,connect);
            let msgObj = {
                level: "info",
                system: "agent",
                msg: "SYNCSeverStartFail",
                title: "SYNCSeverStartFail",
                serverName: "",
                sourceId: conf.uuid
            };
            setTimeout(() => {
                connectionSendMsg('postMessage', msgObj);
            }, 2000);
        }
    }
};
/*
function startFun(conf,arguments){
    const outLog = fs.openSync(conf.COMPONENTS_DIR + "/backendDebug.log","a");
    const errLog = fs.openSync(conf.COMPONENTS_DIR + "/backendDebug.log","a");
    let env = process.env;
    env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
    env.TAPDATA_PORT = conf.tapdata_port;
    env.API_SERVER_PORT = conf.api_server_port;//'3080';
    env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
    env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
    env.NODE_HOME = conf.NODE_HOME;
    env.TAPDATA_HOME = conf.TAPDATA_HOME;
    env.ssl = conf.ssl;
    env.sslCA = conf.mongo_sslCAFile;
    env.sslCertKey = conf.mongo_sslPEMKeyFile;
    env.cloud_accessCode = conf.cloud_accessCode;
    env.cloud_retryTime = conf.cloud_retryTime;
    env.cloud_baseURLs = conf.cloud_baseURLs;
    env.mode = conf.mode;
    //env.PATH = conf.NODE_HOME + ':' +env.PATH;
    conf.TAPDATA_JAVA_OPTS = conf.TAPDATA_JAVA_OPTS + "-Dspring.config.location=file:"+conf.WORK_DIR+"/application.yml -Dlogging.config=file:"+conf.TAPDATA_HOME+"/etc/log4j2.yml ";
    let execString = conf.TAPDATA_JAVA_OPTS + "-jar "  + conf.COMPONENTS_DIR + "/" + conf.AGENT_JAR_FILE;//-Djava.security.egd=file:/dev/./urandom -Duser.timezone=GMT+08
    execString = basic.getExecStr(conf,execString);
    let stdio = ['ignore',outLog,errLog];
    if(basic.isDebug(arguments)){
        console.info("start backend env :");
        console.info(env);
        console.info("start backend args :");
        console.info(execString);
        console.info("start frontend cwd :");
        console.info(env.TAPDATA_HOME);
        //stdio = ['inherit','inherit','inherit'];
    }
    let subProcess = require('child_process').spawn('java',execString.split(" "), {
        'env': env,
        'cwd':env.TAPDATA_HOME,
        'stdio':stdio,
        'detached': true
    });
    subProcess.unref();
    console.info("backend service started.");
}

 */
function sleep (time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
function getJavaVersion(javaPath){
    return new Promise((resolve,reject)=>{
        let str = javaPath + " -version";
        child_process.exec(str,{windowsHide:true}, (error, stdout, stderr) => {
                if (error) {
                    reject();
                }
                let JDKVersion = stderr.split("version")[1].split('"')[1].substring(0, 3);
                resolve(JDKVersion);
            }
        );
    })
}