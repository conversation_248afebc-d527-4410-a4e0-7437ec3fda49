# -*- coding:utf8 -*-

import argparse
import copy
import os
import xml.etree.ElementTree as ET


class BaseParser:

    _arg_instance = None
    _args = None

    @classmethod
    def parser(cls):
        if not hasattr(cls, "description"):
            raise NotImplementedError("description must be set")
        if cls._arg_instance is None:
            cls._arg_instance = argparse.ArgumentParser(description=cls.description)
        return cls._arg_instance

    @classmethod
    def args(cls):
        if cls._args is None:
            if not hasattr(cls, "arguments") or len(cls.arguments) == 0:
                raise NotImplementedError("arguments must be set")
            arguments = copy.deepcopy(cls.arguments)
            for arg in arguments:
                name = arg["name"]
                del arg["name"]
                cls.parser().add_argument(*name, **arg)
            cls._args = cls.parser().parse_args()
        return cls._args


class BuildParser(BaseParser):

    description = """云版编译优化工具
    1. 编译指定组件: python build.py -c tm-java -p /Users/<USER>/xxx/pom.xml -n http://maven.apache.org/POM/4.0.0' -m true
    """

    arguments = [
        {"name": ["-c", "--component"], "help": "组件名，如tm-java,agent等", "type": str, "required": True},
        {"name": ["-p", "--pom"], "help": "pom文件路径", "type": str, "required": True},
        {"name": ["-n", "--namespace"], "help": "pom文件的命令空间", "type": str, "required": False},
        {"name": ["-m", "--modify_pom"], "help": "修改pom文件", "type": bool, "required": False},
        {"name": ["-s", "--script"], "help": "构建脚本命令", "type": str, "required": False},
    ]

    @classmethod
    def modify_pom(cls):
        """
        修改pom文件中的module
        由于modules是按照依赖顺序排列的，所以遍历modules，直到module == component，将其后的module全部删除
        1. 备份pom文件
        2. 读取pom文件
        3. 遍历modules，直到module == component，将其后的module全部删除
        4. 保存pom文件
        5. 进行编译
        6. 恢复pom文件
        """
        # 1. 备份pom文件
        os.rename(cls.args().pom, cls.args().pom + ".bak")
        try:
            # 2. 读取pom文件
            tree = ET.parse(cls.args().pom + ".bak")
            root = tree.getroot()
            # 3. 遍历modules，直到module == component，将其后的module全部删除
            if cls.args().namespace is not None and len(cls.args().namespace) > 0:
                namespaces = {"mvn": cls.args().namespace}
                modules = root.find('mvn:modules', namespaces)
            else:
                modules = root.find('modules')
            new_modules = []
            for module in modules:
                if module.text != cls.args().component:
                    new_modules.append(module)
                else:
                    new_modules.append(module)
                    break
            # 4. 保存pom文件
            modules.clear()
            for module in new_modules:
                modules.append(module)
            if cls.args().namespace is not None and len(cls.args().namespace) > 0:
                ET.register_namespace('', cls.args().namespace)
            tree.write(cls.args().pom, encoding="utf-8", xml_declaration=True)
            # 5. 进行编译
            os.system("cat " + cls.args().pom)
            os.system(cls.args().script)
            # 6. 恢复pom文件
            os.rename(cls.args().pom + ".bak", cls.args().pom)
        except Exception as e:
            # 6. 恢复pom文件
            os.rename(cls.args().pom + ".bak", cls.args().pom)
            raise e

    @classmethod
    def run(cls):
        if cls.args().modify_pom:
            cls.modify_pom()


if __name__ == "__main__":
    BuildParser.run()
