apiVersion: apps/v1
kind: Deployment
metadata:
  name: drs-console
  namespace: drs
  labels:
    version: -version-
    app: drs-console
spec:
  replicas: 3
  selector:
    matchLabels:
      app: drs-console
  template:
    metadata:
      labels:
        app: drs-console
        version: -version-
    spec:
      hostAliases:
        - ip: *************
          hostnames:
            - "ecloud.10086.cn"
      containers:
        - name: drs-console
          image: tapdata/drs-console:-version-
          imagePullPolicy: IfNotPresent
          env:
            - name: CONFIG_FILE_PATH
              value: "public/dist/config/proxy.json"
          ports:
            - containerPort: 31055
              name: https
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          volumeMounts:
            - mountPath: /apps/public/dist/config
              name: drs-console-proxy-config
      volumes:
        - name: drs-console-proxy-config
          configMap:
            name: drs-console-proxy-config
            items:
              - key: proxy.json
                path: proxy.json
              - key: config.json
                path: config.json
      nodeSelector:
        node-role.kubernetes.io/node: "true"
        drs: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: drs-console-svc
  labels:
    app: drs-console
  namespace: drs
spec:
  ports:
    - port: 31055
      targetPort: 31055
      nodePort: 31055
      name: webhttp
  selector:
    app: drs-console
#  sessionAffinity: ClientIP
  type: NodePort
