/**
 * <AUTHOR>
 * create at 2022/11/15 下午4:36
 */
const tcmServer = {
        "target": "http://*************:30103",
        "grayTarget": "http://*************:31103",
        "secure": false,
        "keepAliveTimeout": 50000
    },
    tmJavaServer = {
        "target": "http://*************:30105",
        "secure": false,
        "grayTarget": "http://*************:31105",
        "pathRewrite": {
            "^/tm/": "/",
            "^/tm_xdfwdsax": ""
        },
        "ws": true,
        "keepAliveTimeout": 50000
    },
    tmJavaNettyServer = {
        "target": "http://*************:30107",
        "secure": false,
        "grayTarget": "http://*************:31107",
        "pathRewrite": {
            "^/tm/": "/"
        },
        "ws": true,
        "keepAliveTimeout": 50000
    },
    consoleServer = {
        "target": "http://*************:30106",
        "grayTarget": "http://*************:31106",
        "secure": false
    },
    qingCloudServer = {
      "target": "http://139.198.127.204:31589",
      "secure": false
    };
module.exports = {
    port: 30100,
    portMetrics: 34567,
    static: '/opt/gateway/public',
    log: {
        level: 'debug',
        // output: 'logs/gateway.log'
    },
    enableSession: true,
    enableCompression: true,
    enableAuth: true,
    mongoSessionStore: {
        mongoUrl: '***************************************************************************************************',
        ttl: 60 * 60,
        autoRemove: 'native',
        collectionName: 'sessions'
    },
    redisSessionStore: {
        url: 'redis://default:Gotapd8!@*************:7379/0',
        legacyMode: true
    },
    tcmBaseUrl: 'http://*************:30103',
    mongoUrl: '***************************************************************************************************',
    tcmDbName: 'dfsTcm',
    httpClientTimeout: 10000,
    authingConfig: {
        ignoreAuth: ['^/health','^/tm_xdfwdsax/',
            '^/(js|css|fonts|img|config/config.json)',
            '^/$', '^/index.html',
            '/api/tcm/user/behavior', '/api/tcm/mp/event', '/api/tcm/queryTransferTotal', '/api/tcm/stripe/webhook'],
        enableAccessKeySignature: true,
        appID: '642be97836ccb3a96945b743',
        appSecret: 'bbe8e6f700c344b0c7bc3ef110fe38ad',
        issuer: 'https://auth.tapdata.io/oidc',
        configInfo: 'https://auth.tapdata.io/oidc/.well-known/openid-configuration',
        callbackUrl: 'https://cloud.tapdata.io/console/auth/cb',
        logoutUrl: `https://auth.tapdata.io/login/profile/logout?redirect_uri=${encodeURIComponent('https://cloud.tapdata.io')}`,
        successUrl: '/console',
        loginPath: '/login',
        logoutPath: '/logout',
        context: '/console',
        mongoSessionStore: {
            mongoUrl: '***************************************************************************************************',
            collectionName: 'passport_user_cache'
        },
        redisSessionStore: {
            url: 'redis://default:Gotapd8!@*************:7379/1',
            legacyMode: true
        },
        timeout: 20000
    },
    pathRewrite: {
        "^/console/": "/",
        "^/console": "/"
    },
    proxy: {
        '/api/tcm': tcmServer,
        '/version': tcmServer,
        '/tm/engine/': Object.assign({
            "router": function(req) {
                if (req.grayScale) {
                    return tmJavaNettyServer.grayTarget;
                }
                return tmJavaNettyServer.target;
            }
        }, tmJavaNettyServer),
        '/tm/': Object.assign({
            "router": function(req) {
                if (req.grayScale) {
                    return tmJavaServer.grayTarget;
                }
                return tmJavaServer.target;
            }
        }, tmJavaServer),
        '/tm/ws/': tmJavaServer,
        '/private_ask': qingCloudServer,
        '/': consoleServer
    }
}