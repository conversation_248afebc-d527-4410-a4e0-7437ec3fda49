spring:
  profiles:
    active: 'prod'
    include: 'dfs'

  data:
    mongodb:
      uri: 'mongodb://tcm:|>k<Glzq~}!6@**************:37017,*************:37017,**************:37017/dfsTcm?authSource=dfsTcm'
      cursorBatchSize: 1000
      tmDb: dfsTm

tm:
  domain: *************:30105
  appKey: 60125b2c24a4973ee4420646
  appSecret: 60125b2c24a4973ee44206471
  backendUrl: http://*************:30105/tm/api/

site:
    #type: domesticStation
    type: internationalStation

Stripe:
    apikey: ***********************************************************************************************************
    endpointSecret: whsec_WJTqgsl1OpltKJy648i1utQcEAbEpCjp

# DFS Authing config
authing:
  userPoolId: 642bd740209925ea00cccaeb
  userPoolSecret: 92d6242ca305ef43158989093f64aada
  host: 'https://console.us.authing.co'

orderlimit: 1

agentCleanUpTime: 7
agentAlarmTime: 6
tasks:
  agentCleanUp:
    cron: 0 0 20 * * ?
    lockDuration: PT1H
  agentAlarm:
    cron: 0 0 10 * * ?
    lockDuration: PT1H
aliyun:
  sms:
    regionId: cn-hangzhou
    accessKey: LTAI5tRs7Bi4t4ngvH94uX17
    accessSecret: ******************************
    domain: dysmsapi.aliyuncs.com
    version: 2017-05-25
    signName: Tapdata
mail:
  config:
    path: 'classpath:/mail.properties'

management:
  server:
    port: 34567
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: tcm
