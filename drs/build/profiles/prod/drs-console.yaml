kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-console-proxy-config
  namespace: drs
  labels:
    app: drs-console-proxy-config
data:
  proxy.json: |
    {
        "useSsl": false,
        "useCas": false,
        "userSafe": false,
        "port": 31055,
        "casServerPath": "https://ecloud.10086.cn",
        "casServicePreFix": "https://order.cmecloud.cn:31055",
        "casIgnore": ["/health", "/version"],
        "logPath": "logs",
        "sessionConfig": {
            "cookie": {
                "secure": true,
                "sameSite": "none"
            },
            "proxy": "force"
        },
        "proxyConfig": [
             {
                "name": "drs-tm-api-health",
                "context": "/health",
                "needToken": false,
                "needUserName": false,
                "options": {
                    "target": "http://10.213.174.210:30103",
                    "secure": false
                }
             }
        ]
    }

  config.json: |
    {
      "BASE_URL": "https://ecloud.10086.cn",
      "CONSOLE_BASE_URL": "https://console.ecloud.10086.cn",
      "WS_BASE_URL": "console.ecloud.10086.cn",
      "ENV": "prod",
      "POOLS": ["CIDC-RP-25", "CIDC-RP-29", "CIDC-RP-35", "CIDC-RP-26", "CIDC-RP-33"]
    }

---
kind: ConfigMap
apiVersion: v1
metadata:
  name: tap-manager-order-nginx-config
  namespace: drs
  labels:
    app: tap-manager-order-nginx-config
data:
  default.conf: |
    server {

        listen       80;
        listen       [::]:80;

        location / {
            root /apps/public/dist;
        }

        error_page  405 =200 $uri;
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: drs-console
  namespace: drs
  labels:
    version: -version-
    app: drs-console
spec:
  replicas: 3
  selector:
    matchLabels:
      app: drs-console
  template:
    metadata:
      labels:
        app: drs-console
        version: -version-
    spec:
      hostAliases:
        - ip: *************
          hostnames:
            - "ecloud.10086.cn"
      containers:
        - name: drs-console
          image: tapdata/drs-console:-version-
          imagePullPolicy: IfNotPresent
          env:
            - name: CONFIG_FILE_PATH
              value: "public/dist/config/proxy.json"
          ports:
            - containerPort: 31055
              name: https
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: tap-manager-order-nginx-config
            - mountPath: /apps/public/dist/config
              name: drs-console-proxy-config
      volumes:
        - name: tap-manager-order-nginx-config
          configMap:
            name: tap-manager-order-nginx-config
        - name: drs-console-proxy-config
          configMap:
            name: drs-console-proxy-config
            items:
              - key: proxy.json
                path: proxy.json
              - key: config.json
                path: config.json
      nodeSelector:
        node-role.kubernetes.io/node: "true"
        drs: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: drs-console-svc
  labels:
    app: drs-console
  namespace: drs
spec:
  ports:
    - port: 31055
      targetPort: 80
      nodePort: 31055
      name: webhttp
  selector:
    app: drs-console
#  sessionAffinity: ClientIP
  type: NodePort
