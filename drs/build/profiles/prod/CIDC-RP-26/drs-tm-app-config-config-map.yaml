kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-tm-app-config
  namespace: drs
  labels:
    app: drs-tm-app-config
data:
  datasources.json: |
    {
        "db": {
                "name": "db",
                "connector": "memory"
        },
        "mongoDs": {
                "url": "mongodb://root:Gotapd8!@10.168.116.51:27017,10.168.116.52:27017,10.168.116.53:27017/tapdata_tm?replicaSet=rs&authSource=admin",
                "name": "mongoDs",
                "connector": "mongodb",
                "poolSize": 100,
                "allowExtendedOperators": true,
                "useNewUrlParser": true,
                "ssl": false,
                "sslKey": null,
                "sslCert": null,
                "sslPass": null,
                "sslValidate": false,
                "sslCA": [],
                "checkServerIdentity": false,
                "serverSelectionTimeoutMS": 60000,
                "connectTimeoutMS": 20000,
                "socketTimeout": 720000
        },
        "eDs": {
                "name": "eDs",
                "connector": "mail",
                "transports": [
                        {
                                "type": "smtp",
                                "host": "smtp.exmail.qq.com",
                                "secure": true,
                                "port": 465,
                                "tls": {
                                        "rejectUnauthorized": false
                                },
                                "auth": {
                                        "user": "",
                                        "pass": ""
                                }
                        }
                ]
        }
    }

