apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: drs-tcm
    version: -version-
  name: drs-tcm
  namespace: drs
spec:
  replicas: 3
  selector:
    matchLabels:
      app: drs-tcm
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: drs-tcm
        version: -version-
    spec:
      hostAliases:
        - ip: **************
          hostnames:
            - "ecloud.10086.cn"
        - ip: ************
          hostnames:
            - "ccops-paas.cmecloud.cn"
      containers:
        - image: registry.paas:80/drs/tapdata/drs-tcm:-version-
          name: drs-tcm
          resources:
            limits:
              cpu: "4"
              memory: "8Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          ports:
            - containerPort: 3000
              name: java
              protocol: TCP
          volumeMounts:
            - mountPath: /opt/tcm/conf
              name: drs-tcm-app-config
            - mountPath: /opt/tcm/logs
              name: drs-tcm-app-logs
      volumes:
        - name: drs-tcm-app-logs
          hostPath:
            path: /apps/logs/drs
            type: Directory
        - name: drs-tcm-app-config
          configMap:
            name: drs-tcm-app-config
            items:
              - key: application.yml
                path: application.yml
              - key: logback.xml
                path: logback.xml
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      tolerations:
        - key: "node-role.kubernetes.io/master"
          operator: "Exists"
          effect: "NoSchedule"

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: drs-tcm-svc
  labels:
    app: drs-tcm-svc
  namespace: drs
spec:
  type: NodePort
  ports:
    - port: 3000
      protocol: TCP
      targetPort: 3000
      nodePort: 30103
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: drs-tcm
