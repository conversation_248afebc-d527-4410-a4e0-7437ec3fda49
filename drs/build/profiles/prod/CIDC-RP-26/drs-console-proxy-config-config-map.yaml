kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-console-proxy-config
  namespace: drs
  labels:
    app: drs-console-proxy-config
data:
  proxy.json: |
    {
        "useSsl": false,
        "useCas": false,
        "userSafe": false,
        "port": 31055,
        "casServerPath": "https://ecloud.10086.cn",
        "casServicePreFix": "https://order.cmecloud.cn:31055",
        "casIgnore": ["/health", "/version"],
        "logPath": "logs",
        "sessionConfig": {
            "cookie": {
                "secure": true,
                "sameSite": "none"
            },
            "proxy": "force"
        },
        "proxyConfig": [
             {
                "name": "drs-tm-api-health",
                "context": "/health",
                "needToken": false,
                "needUserName": false,
                "options": {
                    "target": "http://10.213.174.210:30103",
                    "secure": false
                }
             }
        ]
    }

  config.json: |
    {
      "BASE_URL": "https://ecloud.10086.cn",
      "CONSOLE_BASE_URL": "https://console.ecloud.10086.cn",
      "WS_BASE_URL": "console-wuxi-1.cmecloud.cn:8443",
      "ENV": "prod",
      "POOLS": ["CIDC-RP-25", "CIDC-RP-29", "CIDC-RP-33", "CIDC-RP-35", "CIDC-RP-26"]
    }
