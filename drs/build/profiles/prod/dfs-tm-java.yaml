apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: dfs-tm-java
    version: -version-
  name: dfs-tm-java
  namespace: dfs
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dfs-tm-java
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: dfs-tm-java
    spec:
      hostAliases:
#        - ip: *************
#          hostnames:
#            - "lb.kubesphere.local"
      imagePullSecrets:
        - name: acr-auth
      containers:
        - image: registry.cn-beijing.aliyuncs.com/tapdata/dfs-tm-java:-version-
          name: dfs-tm-java
#          command: ["java"]
#          args:
#            - "-jar"
#            - "-server"
#            - "-Xms8g"
#            - "-Xmx8g"
#            - "-Xmn3g"
#            - "-XX:CompressedClassSpaceSize=1024m"
#            - "-XX:MetaspaceSize=512m"
#            - "-XX:MaxMetaspaceSize=1024m"
#            - "-XX:+UseConcMarkSweepGC"
#            - "-XX:+CMSScavengeBeforeRemark"
#            - "-XX:+HeapDumpOnOutOfMemoryError"
#            - "-XX:HeapDumpPath=/opt/tm/logs/"
#            - "/opt/tm/lib/tm--version-.jar"
#            - "--spring.config.additional-location=file:./conf/"
#            - "--logging.config=file:./conf/logback.xml"
          resources:
            limits:
              cpu: "6"
              memory: "12Gi"
            requests:
              cpu: "1"
              memory: "2Gi"
          ports:
            - containerPort: 3000
              name: tomcat
              protocol: TCP
            - containerPort: 8246
              name: netty
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: tomcat
            initialDelaySeconds: 20
            periodSeconds: 20
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: /
              port: tomcat
            initialDelaySeconds: 10
            periodSeconds: 10
          volumeMounts:
            -   mountPath: /opt/tm/conf
                name: dfs-tm-java-app-config
            -   mountPath: /opt/tm/logs
                name: dfs-tm-java-app-logs
      volumes:
        - name: dfs-tm-java-app-logs
          hostPath:
            path: /data/logs/dfs
            type: DirectoryOrCreate
        - name: dfs-tm-java-app-config
          configMap:
            name: dfs-tm-java-app-config
            items:
              - key: application.yml
                path: application.yml
              - key: logback.xml
                path: logback.xml
      #nodeSelector:
      #    labelName: node-role.kubernetes.io/master

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: dfs-tm-java-svc
  labels:
    app: dfs-tm-java-svc
  namespace: dfs
spec:
  type: NodePort
  ports:
    - port: 3000
      name: tomcat
      protocol: TCP
      targetPort: 3000
      nodePort: 30105
    - port: 8246
      name: netty
      protocol: TCP
      targetPort: 8246
      nodePort: 30107
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: dfs-tm-java
