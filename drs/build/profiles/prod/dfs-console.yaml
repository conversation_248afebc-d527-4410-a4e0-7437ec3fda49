kind: ConfigMap
apiVersion: v1
metadata:
  name: dfs-console-config
  namespace: dfs
  labels:
    app: dfs-console-config
data:
  config.json: |
    {
      "ENV": "prod",
      "USER_CENTER": "https://tapdata.authing.cn/u",
      "currencyType": "cny",
      "unsubscribeHelpDocumentation": "https://deploy-preview-75--tapdata.netlify.app/cloud/billing/refund#%E9%80%80%E6%AC%BE%E8%AF%B4%E6%98%8E",
      "station": "domestic"
    }
  default.conf: |
    server {

        listen       80;
        listen       [::]:80;

        location / {
            root /apps/console/public;
        }
    
        location = /console/v3 {
            return 302 /console/v3/;
        }
    
        location = /console {
            return 302 /console/v3/;
        }
    
        location = /console/ {
            return 302 /console/v3/;
        }

        error_page  405 =200 $uri;
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dfs-console
  namespace: dfs
  labels:
    version: -version-
    app: dfs-console
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dfs-console
  template:
    metadata:
      labels:
        app: dfs-console
    spec:
      hostAliases:
      imagePullSecrets:
        - name: acr-auth
      containers:
        - name: dfs-console
          image: registry.cn-beijing.aliyuncs.com/tapdata/dfs-console:-version-
          imagePullPolicy: IfNotPresent
#          command: ["node"]
#          args: ["--max-http-header-size=524288","index.js"]
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 20
            periodSeconds: 20
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          volumeMounts:
            - name: dfs-console-config
              mountPath: /apps/console/public/config/config.json
              subPath: config.json
            - name: dfs-nginx-config
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
      volumes:
        - name: dfs-console-config
          configMap:
            name: dfs-console-config
            items:
              - key: config.json
                path: config.json
        - name: dfs-nginx-config
          configMap:
            name: dfs-console-config
            items:
              - key: default.conf
                path: default.conf
---
apiVersion: v1
kind: Service
metadata:
  name: dfs-console-svc
  labels:
    app: dfs-console
  namespace: dfs
spec:
  type: NodePort
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 30106
  selector:
    app: dfs-console
#  sessionAffinity: ClientIP
