/**
 * <AUTHOR>
 * @date 2021/4/3 上午10:44
 * @description
 */
db.drs_k8s_config.insertOne({
    "region" : "CIDC-RP-25",
    "regionName" : "华东-苏州",
    "zone" : "CIDC-RP-25-57",
    "zoneName" : "可用区一",
    "enabled" : true,
    "defaultAgentNamespace" : "drs-agent",
    "defaultAgentVersion" : "0.0.8-prod",
    "registry" : "registry.paas:80/drs",
    "defaultAgentPackage" : "tapdata/drs-agent",
    "enableBandwidth": false,
    "defaultNodeSelector" : {
        "node-role.kubernetes.io/node" : true
    },
    "nodeConfigs" : [
        {
            "ipv4" : "************",
            "ipv6" : "2409:8c20:1833:1000::ad6:8d04"
        }
    ]
});


let productVips = [{
    "productType" : "mysql",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8C20:1833:FFFF:AAD:94C9"
        }, {
            "zoneCode" : "CIDC-RP-25-58",
            "zoneName" : "可用区二",
            "ipv4" : "**************",
            "ipv6" : "2409:8C20:1833:FFFF:AAD:94C9"
        }
    ]
}, {
    "productType" : "mysql pxc",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833:1000::FFFE:AAF:D4C9"
        }
    ]
}, {
    "productType" : "mysql pxc",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833:1000::FFFE:AAF:D4CA"
        }
    ]
}, {
    "productType" : "mysql pxc",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833:1000::FFFE:AAF:D4CA"
        }
    ]
}, {
    "productType" : "mariadb",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-58",
            "zoneName" : "可用区二",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833::FFFE:AAD:94CA"
        }
    ]
}, {
    "productType" : "sqlserver",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833:1000::FFFF:aaf:abc9"
        }
    ]
}, {
    "productType" : "postgres",
    "poolId" : "CIDC-RP-25",
    "poolName" : "华东-苏州",
    "zoneInfo" : [
        {
            "zoneCode" : "CIDC-RP-25-57",
            "zoneName" : "可用区一",
            "ipv4" : "**************",
            "ipv6" : "2409:8c20:1833:1000::FFFF:aaf:a7b4"
        }
    ]
}];

db.drs_product_vip.deleteMany({});
productVips.forEach((vip => db.drs_product_vip.insertOne(vip)));
