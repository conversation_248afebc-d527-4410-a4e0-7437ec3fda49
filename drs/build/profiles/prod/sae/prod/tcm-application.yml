spring:
  profiles:
    active: 'prod'
    include: 'dfs'

  data:
    mongodb:
      uri: 'mongodb://tcm:<EMAIL>:3717,dds-2ze990d6029d16e42796.mongodb.rds.aliyuncs.com:3717/dfsTcmprod?replicaSet=mgset-49010163&authSource=admin'
      cursorBatchSize: 1000
      tmDb: dfsTm

tm:
  domain: 172.23.204.152:30105
  appKey: 60125b2c24a4973ee4420646
  appSecret: 60125b2c24a4973ee44206471
  backendUrl: http://172.23.204.152:30104/tm/api/

# DFS Authing config
authing:
  userPoolId: 5ed5cfc063690a8107edf079
  userPoolSecret: 1ed743a71915b8ff25fe37bc3aac6ab9

orderlimit: 1

agentCleanUpTime: 7
agentAlarmTime: 6
tasks:
  agentCleanUp:
    cron: 0 0 20 * * ?
    lockDuration: PT1H
  agentAlarm:
    cron: 0 0 10 * * ?
    lockDuration: PT1H
aliyun:
  sms:
    regionId: cn-hangzhou
    accessKey: LTAI5tRs7Bi4t4ngvH94uX17
    accessSecret: ******************************
    domain: dysmsapi.aliyuncs.com
    version: 2017-05-25
    signName: Tapdata
mail:
  config:
    path: 'classpath:/mail.properties'

management:
  server:
    port: 34567
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: tcm
