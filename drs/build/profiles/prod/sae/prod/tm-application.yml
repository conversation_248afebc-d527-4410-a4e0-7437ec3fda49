spring:
  profiles:
    active: 'prod'
    include: 'dfs,default'

  data:
    mongodb:
      uri: 'mongodb://tm-java:<EMAIL>:3717,dds-2ze990d6029d16e42796.mongodb.rds.aliyuncs.com:3717/dfsTm?replicaSet=mgset-49010163&authSource=admin'
      cursorBatchSize: 1000
  elasticsearch:
    rest:
      uris: http://172.19.137.196:9200,http://172.19.137.194:9200,http://172.19.137.195:9200

logWareHouse: elasticsearch

task:
  log:
    expireDay: 7
    cron: "0 0 0 * * ?"

tcm:
  url: http://172.23.204.152:30103

management:
  server:
    port: 34567
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: tm-java
