spring:
  profiles:
    active: 'prod3'
    include: 'dfs,default'

  data:
    mongodb:
      uri: 'mongodb://tmv3:<EMAIL>:3717,dds-2ze990d6029d16e42796.mongodb.rds.aliyuncs.com:3717/dfsTm3?replicaSet=mgset-49010163&authSource=admin'
      cursorBatchSize: 1000
  elasticsearch:
    rest:
      uris: http://172.19.137.196:9200,http://172.19.137.194:9200,http://172.19.137.195:9200

tcm:
  url: http://172.31.119.19:30103

logWareHouse: elasticsearch

task:
  log:
    expireDay: 7
    cron: "0 0 0 * * ?"
    indexName: "logs3"

management:
  server:
    port: 34567
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: tm

aliyun:
  accessKey: LTAI5tRs7Bi4t4ngvH94uX17
  accessSecret: ******************************

gateway:
  secret: Q3HraAbDkmKoPzaBEYzPXB1zJXmWlQ169
