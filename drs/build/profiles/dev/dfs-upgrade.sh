#!/usr/bin/env bash

if [[ ! "$1" ]]; then
  echo "Please specify package"
  exit 0
fi

WORK_DIR=$(pwd)
PACKAGE="$1"
DRS_HOME=$(cd "$PACKAGE" && pwd)
DRS_CONFIG="$DRS_HOME/conf"
FILENAME=${DRS_HOME##*/}
UPGRADE_COMPONENTS="$2"
SCRIPT_DIR=$( cd "$( dirname "$0"  )" && pwd )
if [[ ! "$UPGRADE_COMPONENTS" ]]; then
  UPGRADE_COMPONENTS="tcm,tm,agent,console"
fi
#TAP_MANAGER_HOME=${FILENAME%.tar*}
VERSION=${FILENAME#drs-}

#DOCKER_IMAGE="tap-manager-image-$TAP_MANAGER_VERSION.tar"

cat <<_END_
Worker directory:         $WORK_DIR
Script directory:         $SCRIPT_DIR
DRS package:              $FILENAME
DRS home directory:       $DRS_HOME
DRS config directory:     $DRS_CONFIG

DRS Version:              $VERSION
DRS upgrade components:   $UPGRADE_COMPONENTS

_END_

cd "$DRS_HOME" || exit 1

IFS=" " read -r -a COMPONENTS <<< "$(echo "$UPGRADE_COMPONENTS" | tr -d ' ' | tr ',' ' ')"


for COMPONENT in "${COMPONENTS[@]}"; do
  case $COMPONENT in
  "tcm")
    kubectl -n dfs delete configmap dfs-tcm-app-config
    kubectl -n dfs create configmap dfs-tcm-app-config \
      --from-file="$DRS_CONFIG/application.yml" \
    	--from-file="$DRS_CONFIG/logback.xml"
    kubectl apply -f "$DRS_CONFIG/dfs-tcm.yaml"
  ;;
  "tm")
    kubectl apply -f "$DRS_CONFIG/dfs-tm.yaml"
  ;;
  "console")
    kubectl apply -f "$DRS_CONFIG/dfs-console.yaml"
  ;;
  "agent")
    bash "$SCRIPT_DIR" "$PACKAGE"
  ;;
  esac
done







