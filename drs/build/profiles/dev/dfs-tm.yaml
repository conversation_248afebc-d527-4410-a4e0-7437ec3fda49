kind: ConfigMap
apiVersion: v1
metadata:
  name: dfs-tm-app-config
  namespace: dfs
  labels:
    app: dfs-tm-app-config
data:
  datasources.json: |
    {
    	"db": {
    		"name": "db",
    		"connector": "memory"
    	},
    	"mongoDs": {
    		"url": "mongodb://*************:32000/tapdata_tm",
    		"name": "mongoDs",
    		"connector": "mongodb",
    		"poolSize": 100,
    		"allowExtendedOperators": true,
    		"useNewUrlParser": true,
    		"ssl": false,
    		"sslKey": null,
    		"sslCert": null,
    		"sslPass": null,
    		"sslValidate": false,
    		"sslCA": [],
    		"checkServerIdentity": false,
    		"serverSelectionTimeoutMS": 60000,
    		"connectTimeoutMS": 20000,
    		"socketTimeout": 720000
    	},
    	"eDs": {
    		"name": "eDs",
    		"connector": "mail",
    		"transports": [
    			{
    				"type": "smtp",
    				"host": "smtp.exmail.qq.com",
    				"secure": true,
    				"port": 465,
    				"tls": {
    					"rejectUnauthorized": false
    				},
    				"auth": {
    					"user": "",
    					"pass": ""
    				}
    			}
    		]
    	}
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: dfs-tm
    version: -version-
  name: dfs-tm
  namespace: dfs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dfs-tm
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: dfs-tm
    spec:
      containers:
        - image: harbor.devops.tapdata.net:80/dfs/tapdata/dfs-tm:-version-
          name: dfs-tm
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "1"
              memory: "2Gi"
          ports:
            - containerPort: 3030
              name: node
              protocol: TCP
          volumeMounts:
            - name: dfs-tm-app-config
              mountPath: /opt/tm/server/datasources.json
              subPath: datasources.json
          command: ["node"]
          args: ["--stack-trace-limit=100", "/opt/tm/server/index.js"]
          env:
            - name: "frontend_worker_count"
              value: "1"
            - name: "authingUserPoolId"
              value: "60b061312a380ae777c3aafb"
            - name: "authingSecret"
              value: "517736de5054630ea3082fa0d1ec8286"
            - name: "TCM_BASE_URI"
              value: "http://*************:30103/api/tcm/"
      #nodeSelector:
      #  kubernetes.io/hostname: "tapsupertest2"
      volumes:
        #- name: dfs-tm-app-logs
        #  hostPath:
        #    path: /apps/logs/mongodb
        #    type: Directory
        - name: dfs-tm-app-config
          configMap:
            name: dfs-tm-app-config
            items:
              - key: datasources.json
                path: datasources.json
      #nodeSelector:
      #  dfs-tm: "true"

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: dfs-tm-svc
  labels:
    app: dfs-tm-svc
  namespace: dfs
spec:
  type: NodePort
  ports:
    - port: 3030
      protocol: TCP
      targetPort: 3030
      nodePort: 30102
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: dfs-tm
