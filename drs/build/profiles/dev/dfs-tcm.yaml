apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: dfs-tcm
    version: -version-
  name: dfs-tcm
  namespace: dfs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dfs-tcm
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: dfs-tcm
    spec:
      hostAliases:
        - ip: *************
          hostnames:
            - "lb.kubesphere.local"
      containers:
        - image: harbor.devops.tapdata.net:80/dfs/tapdata/dfs-tcm:-version-
          name: dfs-tcm
          resources:
            limits:
              cpu: "4"
              memory: "8Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          ports:
            - containerPort: 3000
              name: java
              protocol: TCP
          volumeMounts:
            -   mountPath: /opt/tcm/conf
                name: dfs-tcm-app-config
            -   mountPath: /opt/tcm/logs
                name: dfs-tcm-app-logs
      #nodeSelector:
      #  kubernetes.io/hostname: "tapsupertest2"
      volumes:
        - name: dfs-tcm-app-logs
          hostPath:
            path: /data/dfs/logs
            type: Directory
        - name: dfs-tcm-app-config
          configMap:
            name: dfs-tcm-app-config
            items:
              - key: application.yml
                path: application.yml
              - key: logback.xml
                path: logback.xml
      #nodeSelector:
      #    labelName: node-role.kubernetes.io/master

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: dfs-tcm-svc
  labels:
    app: dfs-tcm-svc
  namespace: dfs
spec:
  type: NodePort
  ports:
    - port: 3000
      protocol: TCP
      targetPort: 3000
      nodePort: 30103
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: dfs-tcm
