apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: dfs-tm-java
    version: -version-
  name: dfs-tm-java
  namespace: dfs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dfs-tm-java
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: dfs-tm-java
    spec:
      hostAliases:
      imagePullSecrets:
        - name: acr-auth
      containers:
        - image: ccr.ccs.tencentyun.com/tapdfs/dfs-tm-java:-version-
          name: dfs-tm-java
#          command: ["java"]
#          args:
#            - "-jar"
#            - "-server"
#            - "-Xms1g"
#            - "-Xmx8g"
#            - "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
#            - "-XX:CompressedClassSpaceSize=512m"
#            - "-XX:MetaspaceSize=512m"
#            - "-XX:MaxMetaspaceSize=512m"
#            - "/opt/tm/lib/tm--version-.jar"
#            - "--spring.config.additional-location=file:./conf/"
#            - "--logging.config=file:./conf/logback.xml"
          resources:
            limits:
              cpu: "6"
              memory: "8Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          ports:
            - containerPort: 3000
              name: java
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: java
            initialDelaySeconds: 20
            periodSeconds: 20
          readinessProbe:
            httpGet:
              path: /
              port: java
            initialDelaySeconds: 10
            periodSeconds: 10
          volumeMounts:
            -   mountPath: /opt/tm/conf
                name: dfs-tm-java-app-config
            -   mountPath: /opt/tm/logs
                name: dfs-tm-java-app-logs
      volumes:
        - name: dfs-tm-java-app-logs
          hostPath:
            path: /data/logs/dfs/
            type: DirectoryOrCreate
        - name: dfs-tm-java-app-config
          configMap:
            name: dfs-tm-java-app-config
            items:
              - key: application.yml
                path: application.yml
              - key: logback.xml
                path: logback.xml
      #nodeSelector:
      #    labelName: node-role.kubernetes.io/master

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: dfs-tm-java-svc
  labels:
    app: dfs-tm-java-svc
  namespace: dfs
spec:
  type: NodePort
  ports:
    - port: 3000
      name: app
      protocol: TCP
      targetPort: 3000
      nodePort: 30105
    - port: 5005
      name: debug
      protocol: TCP
      targetPort: 5005
      nodePort: 30108
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: dfs-tm-java
