FROM tapdata-docker.pkg.coding.net/wendangshujuku/images/tapdata-ubuntu:18.04.03

ENV LC_ALL="en_US.UTF-8" LANG="en_US.UTF-8"

WORKDIR /opt/gateway

COPY ./conf/application.yml \
     ./lib/cloud-gateway-0.0.1.jar \
     /opt/gateway/

RUN localedef -c -f UTF-8 -i en_US en_US.UTF-8 && \
    mkdir conf && \
    mkdir logs && \
    mkdir lib && \
    mkdir tmpl && \
    mv application.yml conf && \
    mv cloud-gateway-0.0.1.jar lib/

EXPOSE 30100

CMD ["sh", "-c", "java ${JAVA_OPTS} -jar /opt/gateway/lib/cloud-gateway-0.0.1.jar --spring.config.location=file:/opt/gateway/conf/application.yml"]