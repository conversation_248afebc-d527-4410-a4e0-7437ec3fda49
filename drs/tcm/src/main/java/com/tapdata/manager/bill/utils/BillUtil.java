package com.tapdata.manager.bill.utils;

import com.tapdata.manager.agent.entity.Agent;
import com.tapdata.manager.bill.entity.BillDetail;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 话单工具类
 * <pre>
 * Author: <a href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * CreateTime: 2021/4/16 下午5:10
 * </pre>
 */
public class BillUtil {
    private final static String BATCH_NO_PATTERN = "yyyyMMddHH";

    /**
     * 生成批次号
     *
     * @param time 时间
     * @return 批次号（yyyyMMddHH）
     */
    private static String getBatchNo(Date time) {
        return new SimpleDateFormat(BATCH_NO_PATTERN).format(time);
    }

    /**
     * 获取当前批次
     * @return 批次号（yyyyMMddHH）
     */
    public static String currentBatchNo() {
        return getBatchNo(new Date());
    }

    /**
     * 上一小时批次号
     * @return 批次号（yyyyMMddHH）
     */
    public static String lastBatchNo() {
        Calendar sendDatetime = Calendar.getInstance();
        sendDatetime.add(Calendar.HOUR, -1);
        return getBatchNo(sendDatetime.getTime());
    }

    /**
     * 批次号获取区间段
     *
     * @param batchNo 批次号
     * @return [开始时间, 结束时间]
     */
    public static Date[] getDateByBatchNo(String batchNo) {
        try {
            Date batchBegin = new SimpleDateFormat(BATCH_NO_PATTERN).parse(batchNo);
            return new Date[]{
                    batchBegin, new Date(batchBegin.getTime() + 60 * 60 * 1000)
            };
        } catch (ParseException e) {
            return new Date[0];
        }
    }

    /**
     * 获取构建主机
     *
     * @return 构建主机
     */
    public static String getBuildHost() throws UnknownHostException {
        InetAddress localHost = InetAddress.getLocalHost();
        return localHost.getHostName();
    }

    /**
     * 实例转话单详情
     *
     * @param agent   实例
     * @param batchNo 批次号
     * @param host    主机名
     * @return 话单详情
     */
    public static BillDetail parse(Agent agent, String batchNo, String host) {
        BillDetail billDetail = new BillDetail(batchNo, host);
        billDetail.setCreateAt(new Date());
        billDetail.setLastUpdAt(billDetail.getCreateAt());
        billDetail.setInstanceId(agent.getId().toHexString());
        billDetail.setRegion(agent.getRegion());
        billDetail.setSpec(agent.getSpec());
        billDetail.setOrderInfo(agent.getOrderInfo());
        return billDetail;
    }
}
