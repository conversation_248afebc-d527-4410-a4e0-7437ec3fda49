package com.tapdata.manager.bill.utils;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.tapdata.manager.bill.BillConfig;
import com.tapdata.manager.bill.IFtpConnector;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * SFTP 连接器
 * <pre>
 * Author: <a href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * CreateTime: 2021/4/22 下午12:25
 * </pre>
 */
@Slf4j
public class SFTPConnector implements IFtpConnector {

    private Session session;
    private ChannelSftp channel;

    private String server;
    private String remotePath;

    public SFTPConnector(BillConfig conf) throws Exception {
        this.server = conf.getFtpServer();
//        this.localPath = conf.getFtpLocalPath();
        this.remotePath = conf.getFtpRemotePath();
        int timeout;
        if (null == conf.getFtpConnectTimeout()) {
            timeout = DEFAULT_CONNECT_TIMEOUT;
        } else {
            timeout = conf.getFtpConnectTimeout();
        }

        log.info("SFTP连接：server={}, port={}", server, conf.getFtpPort());
        JSch jsch = new JSch();
        if (conf.getFtpPrivateKey() != null && !conf.getFtpPrivateKey().isEmpty()) {
            jsch.addIdentity(conf.getFtpPrivateKey()); // 设置私钥
        }
        session = jsch.getSession(conf.getFtpUsername(), server, conf.getFtpPort());
        if (conf.getFtpPassword() != null) {
            session.setPassword(conf.getFtpPassword());
        }
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect(timeout);

        channel = (ChannelSftp) session.openChannel("sftp");
        channel.connect(timeout);
        log.info("SFTP连接成功：server={}, port={}", server, conf.getFtpPort());
    }

    @Override
    public boolean storeFile(String fileName, InputStream in) throws Exception {
        String remoteFilePath = remotePath(fileName);
        log.info("SFTP上传：server={}, remoteFile={}", server, remoteFilePath);
        if (channel.isConnected()) {
            channel.put(in, remoteFilePath);
            return true;
        }
        log.warn("SFTP上传：server={}, remoteFile={}，未连接", server, remoteFilePath);
        return true;
    }

    @Override
    public boolean rename(String fromName, String toName) throws Exception {
        String fromPath = remotePath(fromName);
        String toPath = remotePath(toName);
        log.info("SFTP重命名：server={}, from={}, to={}", server, fromPath, toPath);
        if (channel.isConnected()) {
            channel.rename(fromPath, toPath);
            return true;
        }
        log.warn("SFTP重命名：server={}, from={}, to={}，未连接", server, fromPath, toPath);
        return false;
    }

    @Override
    public boolean retrieveFile(String fileName, OutputStream out) throws Exception {
        String remoteFilePath = remotePath(fileName);
        log.info("SFTP下载：server={}, remotePath={}", server, remoteFilePath);
        if (channel.isConnected()) {
            channel.get(remoteFilePath, out);
            return true;
        }
        log.warn("SFTP下载：server={}, remotePath={}，未连接", server, remoteFilePath);
        return true;
    }

    @Override
    public void close() throws Exception {
        log.info("SFTP关闭[{}]", server);
        if (null != channel) {
            if (channel.isConnected()) {
                channel.disconnect();
            }
        }
        if (null != session) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }

    private String remotePath(String fileName) {
        return remotePath + SLASH + fileName;
    }
}
