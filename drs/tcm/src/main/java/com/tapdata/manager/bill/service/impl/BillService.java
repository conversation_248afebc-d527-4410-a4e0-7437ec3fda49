package com.tapdata.manager.bill.service.impl;

import com.mongodb.client.result.UpdateResult;
import com.tapdata.manager.agent.AgentStatus;
import com.tapdata.manager.agent.entity.Agent;
import com.tapdata.manager.agent.service.AgentService;
import com.tapdata.manager.bill.BillConfig;
import com.tapdata.manager.bill.BillStatus;
import com.tapdata.manager.bill.entity.Bill;
import com.tapdata.manager.bill.entity.BillDetail;
import com.tapdata.manager.bill.repository.BillDetailRepository;
import com.tapdata.manager.bill.repository.BillRepository;
import com.tapdata.manager.bill.service.IBillService;
import com.tapdata.manager.bill.service.ISendBillService;
import com.tapdata.manager.bill.utils.BillUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * 话单服务
 * <pre>
 * Author: <a href="mailto:<EMAIL>">Harsen</a>
 * CreateTime: 2021/4/19 下午2:22
 * </pre>
 */
@Slf4j
@Service
public class BillService implements IBillService {
    // 按量计费才进话单
    private final static String BILL_CHARGING_MODE = "2";

    @Autowired(required = false)
    private BillConfig conf = new BillConfig();
    private final BillRepository billRepository;
    private final BillDetailRepository billDetailRepository;
    @Autowired
    private  AgentService agentService;
    @Autowired(required = false)
    private ISendBillService sendBillService;

    @Autowired
    public BillService(BillRepository billRepository, BillDetailRepository billDetailRepository) {
        this.billRepository = billRepository;
        this.billDetailRepository = billDetailRepository;
    }

    /**
     * 查询批次对应话单
     *
     * @param batchNo 批次
     * @return 话单
     */
    private Bill findBillByBatchNo(String batchNo) {
        return billRepository.findOne(Query.query(Criteria.where("batchNo").is(batchNo))).orElse(null);
    }

    /**
     * 查询当前Agent
     * - 收费模式：usageAmount-2-按使用量
     * - 状态非：Aproving,Deleting,Deleted,Freeze
     *
     * @return Agent集合
     */
    private List<Agent> findCurrentAgent() {
        Query query = Query.query(Criteria
                .where("status").nin(AgentStatus.Approving.name(), AgentStatus.Deleting.name(), AgentStatus.Deleted.name(), AgentStatus.Freeze.name())
                .and("orderInfo.chargingMode").is(BILL_CHARGING_MODE)
        );
//        query.fields().include("id").include("_id").include("spec").include("orderInfo").include("agentType");
        return agentService.findAll(query);
    }

    /**
     * 更新或插入话单详情
     *
     * @param billDetail 话单详情
     */
    private void upsertBillDetailByBatchNo(BillDetail billDetail) {
        billDetailRepository.upsert(Query.query(Criteria
                .where("batchNo").is(billDetail.getBatchNo())
                .and("instanceId").is(billDetail.getInstanceId())
        ), billDetail);
    }

    /**
     * 根据批次号查询话单详情
     *
     * @param batchNo 批次号
     * @return 话单详情集合
     */
    private List<BillDetail> findBillDetailByBatchNo(String batchNo) {
        return billDetailRepository.findAll(Query.query(Criteria.where("batchNo").is(batchNo)));
    }

    @Override
    public void buildBill(String batchNo, String buildHost) throws Exception {
        if (!conf.getBillSend()) {
            log.warn("[{}]话单生成-未启用", batchNo);
            return;
        }
        // 生成话单
        Bill bill = findBillByBatchNo(batchNo);
        if (null != bill) {
            log.warn("[{}]话单生成-已生成", batchNo);
            return;
        }

        log.debug("[{}]话单生成-开始", batchNo);
        bill = billRepository.save(new Bill(batchNo, buildHost));

        try {
            // 加载实例信息
            List<Agent> agents = findCurrentAgent();
            agents.forEach(agent -> {
                BillDetail billDetail = BillUtil.parse(agent, batchNo, buildHost);
                upsertBillDetailByBatchNo(billDetail);
            });

            bill.setStatus(BillStatus.Initialized.name());
            bill.setMessage("初始化");
            billRepository.update(bill);
            log.debug("[{}]话单生成-完成，共 {} 个记录", batchNo, agents.size());
        } catch (Exception e) {
            bill.setStatus(BillStatus.Fail.name());
            bill.setMessage(e.getMessage());
            billRepository.update(bill);
            throw e;
        }
    }

    @Override
    public Bill sendBill(String batchNo, boolean existReset) throws Exception {
        if (!conf.getBillSend()) throw new Exception("未启用");

        Bill bill = findBillByBatchNo(batchNo);
        if (null == bill) {
            throw new Exception("批次不存在");
        } else if (!BillStatus.Initialized.name().equals(bill.getStatus())) {
            if (existReset) {
                // 重置状态数据
                bill.setStatus(BillStatus.Initialized.name());
                bill.setMessage("重置");
                billRepository.update(bill);
            } else {
                throw new Exception("状态异常（" + bill.getStatus() + "）");
            }
        }

        int tryCounts = 0;
        try {
            log.debug("[{}]话单发送-开始", batchNo);
            List<BillDetail> billDetailList = findBillDetailByBatchNo(batchNo);
            if (null == billDetailList || billDetailList.isEmpty()) {
                bill.setStatus(BillStatus.Fail.name());
                bill.setMessage("话单列表为空");
                bill.setTotals(0);
                bill.setTryCounts(0);
                billRepository.update(bill);
                return bill;
            }

            String optHost = BillUtil.getBuildHost();
            bill.setSendHost(optHost);
            bill.setTotals(billDetailList.size());
            bill.setStatus(BillStatus.Generating.name());
            bill.setMessage("开始生成文件");
            billRepository.update(bill);
            log.debug("[{}]话单发送-生成文件", batchNo);

            // 生成文件
            try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                String fileName = sendBillService.generate(bill, batchNo, optHost, billDetailList, os);

                // 发送文件
                bill.setFilePath(fileName);
                bill.setStatus(BillStatus.Sending.name());
                bill.setMessage("开始发送文件");
                billRepository.update(bill);
                log.debug("[{}]话单发送-发送", batchNo);
                for (; true; tryCounts++) {
                    try (ByteArrayInputStream is = new ByteArrayInputStream(os.toByteArray())) {
                        sendBillService.send(batchNo, fileName, is);
                        break;
                    } catch (Exception e) {
                        if (tryCounts < 3) {
                            log.warn("第{}次话单上传 [{}] 至FTP服务器失败", tryCounts, fileName, e);
                        } else {
                            throw e;
                        }
                    }
                }
            }

            bill.setStatus(BillStatus.Ok.name());
            bill.setMessage("成功");
            bill.setTryCounts(tryCounts);
            billRepository.update(bill);
            log.debug("[{}]话单发送-完成，共 {} 个记录", batchNo, billDetailList.size());
        } catch (Exception e) {
            bill.setTryCounts(tryCounts);
            bill.setStatus(BillStatus.Fail.name());
            bill.setMessage(e.getMessage());
            billRepository.update(bill);
            throw e;
        }
        return bill;
    }

    /**
     * 添加新订购实例话单记录，话单周期：创建时间-59分钟59秒
     * @param agent 实例
     * @throws Exception
     */
    @Override
    public void saveBillByCreateAgent(Agent agent) throws Exception {
        log.info("话单详情-添加实例订购：agentId-{}", agent.getId());

        //付费类型为按量计费进话单
        if (BILL_CHARGING_MODE.equals(agent.getOrderInfo().getChargingMode())) {
            String batchNo = BillUtil.currentBatchNo();
            String buildHost = BillUtil.getBuildHost();
            BillDetail billDetail = BillUtil.parse(agent, batchNo, buildHost);
            // 设置开始时间
            billDetail.setBeginTime(agent.getCreateAt());
            upsertBillDetailByBatchNo(billDetail);
        }
    }

    @Override
    public void setBillEndTime(String agentId) throws Exception {
        log.info("话单详情-实例退订：agentId-{}", agentId);

        final String batchNo = BillUtil.currentBatchNo();
        UpdateResult res = billDetailRepository.updateFirst(
                Query.query(Criteria.where("batchNo").is(batchNo).and("instanceId").is(agentId))
                , Update.update("endTime", new Date())
        );
        if (res.getModifiedCount() <= 0) {
            log.warn("[{}]话单详情-实例退订更新失败：agentId-{}", batchNo, agentId);
        }
    }

    /**
     * 实例变更时时，更新话单
     * @param agentId 实例编号
     * @throws Exception
     */
    @Override
    public void setBillChange(String agentId) throws Exception {
        log.info("话单详情-实例变更：agentId-{}", agentId);

        final String batchNo = BillUtil.currentBatchNo();
        UpdateResult res = billDetailRepository.updateFirst(
                Query.query(Criteria.where("batchNo").is(batchNo).and("instanceId").is(agentId))
                , Update.update("isChange", true)
        );
        if (res.getModifiedCount() <= 0) {
            log.warn("[{}]话单详情-实例变更失败：agentId-{}", batchNo, agentId);
        }
    }
}
