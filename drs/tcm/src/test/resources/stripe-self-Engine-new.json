{"type": [{"nickName": {"cpu": "1", "memory": "2", "order": "0", "period": "0", "periodUnit": "month", "name": "small", "limitTask": 2, "chargeProvider": "FreeTier"}, "amount": "0", "interval": "one_time", "periodUnit": "month", "cny": "0"}, {"nickName": {"cpu": "2", "memory": "4", "order": "1", "period": "1", "periodUnit": "month", "name": "large", "limitTask": 5}, "amount": "19900", "interval": "one_time", "periodUnit": "month", "cny": "80000"}, {"nickName": {"cpu": "2", "memory": "4", "order": "2", "period": "1", "periodUnit": "year", "name": "large", "limitTask": 5}, "amount": "214900", "interval": "one_time", "periodUnit": "year", "cny": "864000"}, {"nickName": {"cpu": "4", "memory": "8", "order": "1", "period": "1", "periodUnit": "month", "name": "xlarge", "limitTask": 10}, "amount": "39900", "interval": "one_time", "periodUnit": "month", "cny": "160000"}, {"nickName": {"cpu": "4", "memory": "8", "order": "2", "period": "1", "periodUnit": "year", "name": "xlarge", "limitTask": 10}, "amount": "430900", "interval": "one_time", "periodUnit": "year", "cny": "1728000"}, {"nickName": {"cpu": "8", "memory": "16", "order": "1", "period": "1", "periodUnit": "month", "name": "2xlarge", "limitTask": 20}, "amount": "79900", "interval": "one_time", "periodUnit": "month", "cny": "320000"}, {"nickName": {"cpu": "8", "memory": "16", "order": "2", "period": "1", "periodUnit": "year", "name": "2xlarge", "limitTask": 20}, "amount": "862900", "interval": "one_time", "periodUnit": "year", "cny": "3456000"}, {"nickName": {"cpu": "12", "memory": "24", "order": "1", "period": "1", "periodUnit": "month", "name": "3xlarge", "limitTask": 30}, "amount": "119900", "interval": "one_time", "periodUnit": "month", "cny": "480000"}, {"nickName": {"cpu": "12", "memory": "24", "order": "2", "period": "1", "periodUnit": "year", "name": "3xlarge", "limitTask": 30}, "amount": "1294900", "interval": "one_time", "periodUnit": "year", "cny": "5184000"}, {"nickName": {"cpu": "16", "memory": "32", "order": "1", "period": "1", "periodUnit": "month", "name": "4xlarge", "limitTask": 40}, "amount": "159900", "interval": "one_time", "periodUnit": "month", "cny": "640000"}, {"nickName": {"cpu": "16", "memory": "32", "order": "2", "period": "1", "periodUnit": "year", "name": "4xlarge", "limitTask": 40}, "amount": "1726800", "interval": "one_time", "periodUnit": "year", "cny": "6912000"}, {"nickName": {"cpu": "32", "memory": "64", "order": "1", "period": "1", "periodUnit": "month", "name": "8xlarge", "limitTask": 80}, "amount": "199900", "interval": "one_time", "periodUnit": "month", "cny": "1280000"}, {"nickName": {"cpu": "32", "memory": "64", "order": "2", "period": "1", "periodUnit": "year", "name": "8xlarge", "limitTask": 80}, "amount": "2158900", "interval": "one_time", "periodUnit": "year", "cny": "13824000"}, {"nickName": {"cpu": "2", "memory": "4", "order": "1", "name": "large", "limitTask": 5}, "amount": "18900", "interval": "recurring", "periodUnit": "month", "cny": "76000"}, {"nickName": {"cpu": "2", "memory": "4", "order": "2", "name": "large", "limitTask": 5}, "amount": "214900", "interval": "recurring", "periodUnit": "year", "cny": "864000"}, {"nickName": {"cpu": "4", "memory": "8", "order": "1", "name": "xlarge", "limitTask": 10}, "amount": "37900", "interval": "recurring", "periodUnit": "month", "cny": "152000"}, {"nickName": {"cpu": "4", "memory": "8", "order": "2", "name": "xlarge", "limitTask": 10}, "amount": "430900", "interval": "recurring", "periodUnit": "year", "cny": "1728000"}, {"nickName": {"cpu": "8", "memory": "16", "order": "1", "name": "2xlarge", "limitTask": 20}, "amount": "75900", "interval": "recurring", "periodUnit": "month", "cny": "304000"}, {"nickName": {"cpu": "8", "memory": "16", "order": "2", "name": "2xlarge", "limitTask": 20}, "amount": "862900", "interval": "recurring", "periodUnit": "year", "cny": "3456000"}, {"nickName": {"cpu": "12", "memory": "24", "order": "1", "name": "3xlarge", "limitTask": 30}, "amount": "113900", "interval": "recurring", "periodUnit": "month", "cny": "456000"}, {"nickName": {"cpu": "12", "memory": "24", "order": "2", "name": "3xlarge", "limitTask": 30}, "amount": "1294900", "interval": "recurring", "periodUnit": "year", "cny": "5184000"}, {"nickName": {"cpu": "16", "memory": "32", "order": "1", "name": "4xlarge", "limitTask": 40}, "amount": "151900", "interval": "recurring", "periodUnit": "month", "cny": "608000"}, {"nickName": {"cpu": "16", "memory": "32", "order": "2", "name": "4xlarge", "limitTask": 40}, "amount": "1726900", "interval": "recurring", "periodUnit": "year", "cny": "6912000"}, {"nickName": {"cpu": "32", "memory": "64", "order": "1", "name": "8xlarge", "limitTask": 80}, "amount": "189900", "interval": "recurring", "periodUnit": "month", "cny": "1216000"}, {"nickName": {"cpu": "32", "memory": "64", "order": "2", "name": "8xlarge", "limitTask": 80}, "amount": "2158900", "interval": "recurring", "periodUnit": "year", "cny": "13824000"}]}