{"type": [{"amount": 7920, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "1"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7920, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "2"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 95040, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "3"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 95040, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "4"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 12240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "5"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 12240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "6"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 146880, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "7"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 146880, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "8"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "9"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "10"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "11"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M10", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "12"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "13"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "14"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "15"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "16"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 22320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "17"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 22320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "18"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 267840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "19"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 267840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "20"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 25920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "21"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 25920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "22"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 311040, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "23"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 311040, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "24"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "25"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "26"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "27"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M20", "cpu": "1", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "28"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 51120, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "29"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 51120, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "30"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 613440, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "31"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 613440, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "32"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "33"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "34"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "35"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "36"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 66240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "37"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 66240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "38"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 794880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "39"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 794880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "40"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 82080, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "41"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 82080, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "42"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 984960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "43"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 984960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "HongKong", "name": "M30", "cpu": "2", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "44"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 92493, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "45"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 92493, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "46"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1109916, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "47"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1109916, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "48"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 122409, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "49"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 122409, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "50"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1468908, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "51"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1468908, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "52"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 159804, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "53"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 159804, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "54"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1917648, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "55"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1917648, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M40", "cpu": "4", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "56"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 163746, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "57"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 163746, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "58"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1964952, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "59"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1964952, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "60"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 201140, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "61"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 201140, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "62"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2413680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "63"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2413680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "64"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 238535, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "65"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 238535, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "66"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2862420, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "67"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2862420, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M50", "cpu": "8", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "68"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 324995, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "69"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 324995, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "70"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3899940, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "71"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3899940, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "72"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 362389, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "73"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 362389, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "74"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4348668, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "75"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4348668, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "76"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 399784, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "77"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 399784, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "78"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4797408, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "79"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4797408, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1000", "regionName": "HongKong", "name": "M60", "cpu": "16", "region": "asia-east2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "ASIA_EAST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1000}, "order": "80"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 8640, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "81"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 8640, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "82"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 103680, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "83"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 103680, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "84"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 12960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "85"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 12960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "86"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 155520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "87"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 155520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "88"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 17280, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "89"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 17280, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "90"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 207360, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "91"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 207360, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M10", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "92"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 19440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "93"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 19440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "94"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 233280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "95"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 233280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "96"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 23760, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "97"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 23760, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "98"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 285120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "99"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 285120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "100"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 28080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "101"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 28080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "102"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 336960, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "103"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 336960, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "104"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 37440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "105"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 37440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "106"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 449280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "107"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 449280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M20", "cpu": "1", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "108"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 54000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "109"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 54000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "110"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 648000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "111"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 648000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "112"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 63360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "113"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 63360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "114"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 760320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "115"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 760320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "116"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 72720, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "117"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 72720, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "118"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 872640, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "119"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 872640, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "120"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 92160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "121"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 92160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "122"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1105920, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "123"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1105920, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Sydney", "name": "M30", "cpu": "2", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "124"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 96480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "125"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 96480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "126"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1157760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "127"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1157760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "128"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 133920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "129"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 133920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "130"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1607040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "131"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1607040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "132"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 168480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "133"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 168480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "134"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2021760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "135"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2021760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M40", "cpu": "4", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "136"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 168480, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "137"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 168480, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "138"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2021760, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "139"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2021760, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "140"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 214560, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "141"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 214560, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "142"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2574720, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "143"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2574720, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "144"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 262800, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "145"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 262800, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "146"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3153600, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "147"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3153600, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M50", "cpu": "8", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "148"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 334800, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "149"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 334800, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "150"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4017600, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "151"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4017600, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "152"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 380880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "153"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 380880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "154"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4570560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "155"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4570560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "156"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 429120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "157"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 429120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "158"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 5149440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "159"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 5149440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Sydney", "name": "M60", "cpu": "16", "region": "australia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "AUSTRALIA_SOUTHEAST_1", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "160"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "161"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "162"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "163"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "164"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 10800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "165"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 10800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "166"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 129600, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "167"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 129600, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "168"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 14400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "169"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 14400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "170"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 172800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "171"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 172800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M10", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "172"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 16560, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "173"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 16560, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "174"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 198720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "175"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 198720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "176"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 20160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "177"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 20160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "178"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 241920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "179"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 241920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "180"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 23760, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "181"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 23760, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "182"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 285120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "183"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 285120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "184"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 31680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "185"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 31680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "186"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 380160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "187"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 380160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M20", "cpu": "1", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "188"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 46080, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "189"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 46080, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "190"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 552960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "191"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 552960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "192"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 54000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "193"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 54000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "194"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 648000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "195"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 648000, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "196"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 61200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "197"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 61200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "198"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 734400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "199"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 734400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "200"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 83520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "201"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 83520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "202"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1002240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "203"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1002240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Singapore", "name": "M30", "cpu": "2", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "204"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 96480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "205"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 96480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "206"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1157760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "207"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1157760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "208"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 113040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "209"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 113040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "210"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1356480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "211"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1356480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "212"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 151920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "213"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 151920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "214"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1823040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "215"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1823040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M40", "cpu": "4", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "216"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 145440, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "217"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 145440, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "218"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1745280, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "219"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1745280, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "220"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 182880, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "221"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 182880, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "222"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2194560, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "223"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2194560, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "224"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 222480, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "225"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 222480, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "226"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2669760, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "227"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2669760, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M50", "cpu": "8", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "228"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 289440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "229"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 289440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "230"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3473280, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "231"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3473280, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "232"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 326880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "233"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 326880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "234"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3922560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "235"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3922560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "236"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 365760, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "237"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 365760, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "238"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4389120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "239"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4389120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Singapore", "name": "M60", "cpu": "16", "region": "asia-southeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "SOUTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "240"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "241"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "242"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "243"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "244"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 10080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "245"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 10080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "246"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 120960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "247"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 120960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "248"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 13680, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "249"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 13680, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "250"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 164160, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "251"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 164160, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M10", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "252"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 15840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "253"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 15840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "254"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 190080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "255"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 190080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "256"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "257"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "258"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "259"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "260"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 22320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "261"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 22320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "262"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 267840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "263"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 267840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "264"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 28800, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "265"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 28800, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "266"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 345600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "267"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 345600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M20", "cpu": "1", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "268"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 43200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "269"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 43200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "270"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 518400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "271"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 518400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "272"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 49680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "273"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 49680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "274"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 596160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "275"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 596160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "276"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "277"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "278"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "279"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "280"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 71280, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "281"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 71280, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "282"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 855360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "283"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 855360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Taiwan", "name": "M30", "cpu": "2", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "284"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 77760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "285"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 77760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "286"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 933120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "287"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 933120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "288"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 105120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "289"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 105120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "290"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1261440, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "291"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1261440, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "292"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 140400, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "293"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 140400, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "294"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1684800, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "295"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1684800, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M40", "cpu": "4", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "296"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 136080, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "297"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 136080, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "298"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1632960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "299"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1632960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "300"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 170640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "301"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 170640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "302"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2047680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "303"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2047680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "304"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 205920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "305"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 205920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "306"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2471040, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "307"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2471040, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M50", "cpu": "8", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "308"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 270720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "309"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 270720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "310"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3248640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "311"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3248640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "312"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 304560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "313"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 304560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "314"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3654720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "315"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3654720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "316"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 340560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "317"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 340560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "318"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4086720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "319"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4086720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Taiwan", "name": "M60", "cpu": "16", "region": "asia-east1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "320"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "321"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "322"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "323"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "324"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 12240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "325"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 12240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "326"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 146880, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "327"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 146880, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "328"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 16560, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "329"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 16560, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "330"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 198720, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "331"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 198720, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M10", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "332"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "333"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "334"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "335"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "336"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "337"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "338"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "339"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "340"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 26640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "341"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 26640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "342"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 319680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "343"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 319680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "344"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 35280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "345"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 35280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "346"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 423360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "347"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 423360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M20", "cpu": "1", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "348"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 49680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "349"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 49680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "350"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 596160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "351"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 596160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "352"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "353"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "354"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "355"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "356"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 67680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "357"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 67680, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "358"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 812160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "359"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 812160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "360"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "361"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "362"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1036800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "363"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1036800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Tokyo", "name": "M30", "cpu": "2", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "364"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 88560, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "365"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 88560, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "366"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1062720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "367"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1062720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "368"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 123840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "369"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 123840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "370"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1486080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "371"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1486080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "372"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 169920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "373"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 169920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "374"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2039040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "375"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2039040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M40", "cpu": "4", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "376"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 153360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "377"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 153360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "378"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1840320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "379"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1840320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "380"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 197280, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "381"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 197280, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "382"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2367360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "383"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2367360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "384"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 244080, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "385"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 244080, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "386"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2928960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "387"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2928960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M50", "cpu": "8", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "388"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 304560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "389"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 304560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "390"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3654720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "391"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3654720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "392"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 348480, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "393"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 348480, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "394"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4181760, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "395"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4181760, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "396"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 395280, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "397"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 395280, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "398"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4743360, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "399"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4743360, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Tokyo", "name": "M60", "cpu": "16", "region": "asia-northeast1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "NORTHEASTERN_ASIA_PACIFIC", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "400"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "401"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "402"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "403"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "404"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 11520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "405"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 11520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "406"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 138240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "407"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 138240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "408"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "409"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "410"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "411"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M10", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "412"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "413"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "414"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "415"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "416"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "417"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "418"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "419"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "420"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 25200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "421"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 25200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "422"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 302400, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "423"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 302400, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "424"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "425"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "426"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "427"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M20", "cpu": "1", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "428"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 48960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "429"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 48960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "430"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 587520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "431"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 587520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "432"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "433"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "434"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "435"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "436"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 65520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "437"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 65520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "438"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 786240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "439"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 786240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "440"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 82800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "441"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 82800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "442"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 993600, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "443"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 993600, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "London", "name": "M30", "cpu": "2", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "444"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 87840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "445"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 87840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "446"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1054080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "447"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1054080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "448"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 120240, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "449"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 120240, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "450"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1442880, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "451"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1442880, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "452"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 162720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "453"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 162720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "454"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1952640, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "455"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1952640, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M40", "cpu": "4", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "456"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 152640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "457"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 152640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "458"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1831680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "459"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1831680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "460"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 193680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "461"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 193680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "462"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2324160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "463"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2324160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "464"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 236160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "465"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 236160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "466"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2833920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "467"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2833920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M50", "cpu": "8", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "468"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 303120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "469"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 303120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "470"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3637440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "471"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3637440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "472"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 344160, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "473"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 344160, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "474"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4129920, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "475"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4129920, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "476"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 386640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "477"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 386640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "478"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4639680, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "479"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4639680, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "London", "name": "M60", "cpu": "16", "region": "europe-west2", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "EUROPE_WEST_2", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "480"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "481"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 7200, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "482"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "483"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 86400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "484"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 11520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "485"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 11520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "486"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 138240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "487"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 138240, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "488"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "489"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 15840, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "490"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "491"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 190080, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M10", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "492"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "493"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "494"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "495"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "496"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "497"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 21600, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "498"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "499"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 259200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "500"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 25200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "501"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 25200, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "502"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 302400, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "503"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 302400, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "504"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "505"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 33840, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "506"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "507"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 406080, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M20", "cpu": "1", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "508"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 48960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "509"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 48960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "510"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 587520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "511"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 587520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "512"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "513"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 56880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "514"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "515"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 682560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "516"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 65520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "517"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 65520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "518"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 786240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "519"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 786240, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "520"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 82800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "521"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 82800, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "522"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 993600, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "523"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 993600, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Frankfurt", "name": "M30", "cpu": "2", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "524"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 87840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "525"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 87840, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "526"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1054080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "527"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1054080, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "528"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 120240, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "529"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 120240, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "530"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1442880, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "531"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1442880, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "532"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 162720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "533"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 162720, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "534"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1952640, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "535"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1952640, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M40", "cpu": "4", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "536"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 152640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "537"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 152640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "538"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1831680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "539"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1831680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "540"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 193680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "541"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 193680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "542"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2324160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "543"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2324160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "544"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 236160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "545"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 236160, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "546"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2833920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "547"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2833920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M50", "cpu": "8", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "548"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 303120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "549"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 303120, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "550"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3637440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "551"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3637440, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "552"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 344160, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "553"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 344160, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "554"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4129920, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "555"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4129920, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "556"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 386640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "557"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 386640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "558"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4639680, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "559"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4639680, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Frankfurt", "name": "M60", "cpu": "16", "region": "europe-west3", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "560"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 6480, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "561"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 6480, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "562"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 77760, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "563"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 77760, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "564"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 10800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "565"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 10800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "566"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 129600, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "567"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 129600, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "568"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 14400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "569"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 14400, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "570"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 172800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "571"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 172800, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M10", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "572"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 15120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "573"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 15120, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "574"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 181440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "575"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 181440, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "576"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "577"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 18720, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "578"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "579"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 224640, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "580"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 23040, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "581"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 23040, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "582"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 276480, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "583"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 276480, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "584"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 30240, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "585"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 30240, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "586"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 362880, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "587"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 362880, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M20", "cpu": "1", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "588"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 43200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "589"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 43200, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "590"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 518400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "591"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 518400, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "592"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 51120, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "593"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 51120, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "594"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 613440, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "595"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 613440, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "596"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "597"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 58320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "598"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "599"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 699840, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "600"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 74160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "601"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 74160, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "602"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 889920, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "603"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 889920, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "<PERSON><PERSON>", "name": "M30", "cpu": "2", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "604"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 77040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "605"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 77040, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "606"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 924480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "607"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 924480, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "608"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 107280, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "609"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 107280, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "610"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1287360, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "611"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1287360, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "612"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 146160, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "613"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 146160, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "614"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1753920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "615"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1753920, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M40", "cpu": "4", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "616"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 133920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "617"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 133920, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "618"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1607040, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "619"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1607040, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "620"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 171360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "621"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 171360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "622"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2056320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "623"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2056320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "624"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 210960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "625"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 210960, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "626"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2531520, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "627"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2531520, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M50", "cpu": "8", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "628"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 266400, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "629"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 266400, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "630"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3196800, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "631"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3196800, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "632"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 303840, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "633"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 303840, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "634"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3646080, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "635"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3646080, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "636"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 342720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "637"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 342720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "638"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 4112640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "639"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 4112640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "<PERSON><PERSON>", "name": "M60", "cpu": "16", "region": "us-east4", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "US_EAST_4", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "640"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 5760, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "641"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 5760, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "642"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 69120, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "643"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 69120, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "644"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 9360, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "645"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 9360, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "646"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 112320, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "647"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 112320, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "648"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 12960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "649"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 12960, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "650"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 155520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "651"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 155520, "nickName": {"period": "1", "memory": "2", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M10", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M10", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "652"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 13680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "653"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 13680, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "654"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 164160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "655"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 164160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "10", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 10}, "order": "656"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "657"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 17280, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "658"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "659"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 207360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "50", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 50}, "order": "660"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 20160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "661"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 20160, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "662"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 241920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "663"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 241920, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "664"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 27360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "665"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 27360, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "666"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 328320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "667"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 328320, "nickName": {"period": "1", "memory": "4", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M20", "cpu": "1", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M20", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "668"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 38880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "669"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 38880, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "670"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 466560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "671"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 466560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "672"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 45360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "673"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 45360, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "674"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 544320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "675"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 544320, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "200", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 200}, "order": "676"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 52560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "677"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 52560, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "678"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 630720, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "679"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 630720, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "300", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 300}, "order": "680"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 66960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "681"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 66960, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "682"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 803520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "683"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 803520, "nickName": {"period": "1", "memory": "8", "provider": "Atlas", "storageSize": "512", "regionName": "Oregon", "name": "M30", "cpu": "2", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M30", "providerName": "GCP"}, "diskSizeGB": 512}, "order": "684"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 69120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "685"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 69120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "686"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 829440, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "687"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 829440, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "688"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 95760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "689"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 95760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "690"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1149120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "691"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1149120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "692"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 131760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "693"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 131760, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "694"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1581120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "695"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1581120, "nickName": {"period": "1", "memory": "16", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M40", "cpu": "4", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M40", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "696"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 119520, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "697"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 119520, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "698"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1434240, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "699"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1434240, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "700"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 153360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "701"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 153360, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "702"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 1840320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "703"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 1840320, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "704"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 188640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "705"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 188640, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "706"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2263680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "707"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2263680, "nickName": {"period": "1", "memory": "32", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M50", "cpu": "8", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M50", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "708"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 236880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "709"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 236880, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "710"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 2842560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "711"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 2842560, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "100", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 100}, "order": "712"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 270720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "713"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 270720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "714"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3248640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "715"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3248640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "500", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 500}, "order": "716"}, "interval": "recurring", "periodUnit": "year"}, {"amount": 306720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "717"}, "interval": "one_time", "periodUnit": "month"}, {"amount": 306720, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "month", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "718"}, "interval": "recurring", "periodUnit": "month"}, {"amount": 3680640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "719"}, "interval": "one_time", "periodUnit": "year"}, {"amount": 3680640, "nickName": {"period": "1", "memory": "64", "provider": "Atlas", "storageSize": "1024", "regionName": "Oregon", "name": "M60", "cpu": "16", "region": "us-west1", "periodUnit": "year", "mdbSpec": {"clusterType": "REPLICASET", "providerBackupEnabled": false, "providerSettings": {"regionName": "WESTERN_US", "instanceSizeName": "M60", "providerName": "GCP"}, "diskSizeGB": 1024}, "order": "720"}, "interval": "recurring", "periodUnit": "year"}]}