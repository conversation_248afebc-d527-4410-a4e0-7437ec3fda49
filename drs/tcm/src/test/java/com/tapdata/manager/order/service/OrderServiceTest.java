package com.tapdata.manager.order.service;

import com.alibaba.fastjson.JSON;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.manager.agent.AgentAlterStatus;
import com.tapdata.manager.agent.AgentStatus;
import com.tapdata.manager.agent.AgentType;
import com.tapdata.manager.agent.dto.AgentAlterDto;
import com.tapdata.manager.agent.dto.AgentDto;
import com.tapdata.manager.agent.dto.OrderInfo;
import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.agent.service.AgentAlterService;
import com.tapdata.manager.agent.service.AgentService;
import com.tapdata.manager.base.dto.ResponseMessage;
import com.tapdata.manager.config.security.UserDetail;
import com.tapdata.manager.interactive.service.InteractiveService;
import com.tapdata.manager.exception.MopException;
import com.tapdata.manager.interactive.service.drs.wrapper.*;
import com.tapdata.manager.order.constant.AgentStatusMachine;
import com.tapdata.manager.order.constant.CallbackParamName;
import com.tapdata.manager.order.constant.ChangeSubscribeCode;
import com.tapdata.manager.order.constant.SubscribeCode;
import com.tapdata.manager.order.dto.ChangeSubscribeRequestDTO;
import com.tapdata.manager.order.dto.RenewSubscribeRequestDTO;
import com.tapdata.manager.order.dto.SubscribeRequestDTO;
import com.tapdata.manager.order.dto.UnSubscribeRequestDTO;
import com.tapdata.manager.order.exception.AgentNotExistsException;
import com.tapdata.manager.order.exception.InvalidAgentStatusException;
import com.tapdata.manager.interactive.service.utils.RawMopResultWrapper;
import com.tapdata.manager.user.dto.UserInfoDto;
import org.bson.BsonObjectId;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;
import static org.springframework.data.mongodb.core.query.Update.update;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore(value = "javax.management.*")
@PrepareForTest(OrderService.class)
public class OrderServiceTest {

    @Mock
    InteractiveService interactiveService;

    @Mock
    AgentService agentService;

    @Mock
    AgentAlterService agentAlterService;

    @InjectMocks
    @Spy
    OrderService orderService = new OrderService();

    @Test
    public void checkSubscribe() {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", new HashMap<String, Object>() {{
            put("checkInfo", "OK，校验成功");
            put("checkCode", "0");
        }});
        SubscribeRequestDTO requestDTO = new SubscribeRequestDTO();
        String chargeType = "1";

        doReturn(retMap).when(interactiveService).checkOfferInfo(requestDTO, chargeType, null);

        CheckSubscribeWrapper checkSubscribeWrapper = orderService.checkSubscribe(requestDTO, chargeType, null);
        CheckSubscribeWrapper.Result value = checkSubscribeWrapper.getValue();
        Assert.assertEquals("0", value.getCheckCode());
        Assert.assertEquals("OK，校验成功", value.getCheckInfo());

        Mockito.verify(orderService, Mockito.times(1)).checkSubscribe(requestDTO, chargeType, null);
        Mockito.verify(interactiveService, Mockito.times(1)).checkOfferInfo(requestDTO, chargeType, null);

    }

    @Test
    public void checkSubscribeNotPass() {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", new HashMap<String, Object>() {{
            put("checkInfo", "校验失败");
            put("checkCode", "-1");
        }});
        SubscribeRequestDTO requestDTO = new SubscribeRequestDTO();
        String chargeType = "1";

        doReturn(retMap).when(interactiveService).checkOfferInfo(requestDTO, chargeType, null);

        try {
            orderService.checkSubscribe(requestDTO, chargeType, null);
        } catch (MopException t) {
            ResponseMessage responseMessage = t.toResponse();
            Assert.assertEquals("fail", responseMessage.getCode());
            Assert.assertTrue(responseMessage.getMessage().length() > 0);
        }

        Mockito.verify(orderService, Mockito.times(1)).checkSubscribe(requestDTO, chargeType, null);
        Mockito.verify(interactiveService, Mockito.times(1)).checkOfferInfo(requestDTO, chargeType, null);

    }

    @Test
    public void checkSubscribeFail() {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "-1");
        retMap.put("respDesc", "失败");
        SubscribeRequestDTO requestDTO = new SubscribeRequestDTO();
        String chargeType = "1";

        doReturn(retMap).when(interactiveService).checkOfferInfo(requestDTO, chargeType, null);

        try {
            orderService.checkSubscribe(requestDTO, chargeType, null);
        } catch (MopException t) {
            ResponseMessage responseMessage = t.toResponse();
            Assert.assertEquals("-1", responseMessage.getCode());
            Assert.assertEquals("失败", responseMessage.getMessage());
        }

        Mockito.verify(orderService, Mockito.times(1)).checkSubscribe(requestDTO, chargeType, null);
        Mockito.verify(interactiveService, Mockito.times(1)).checkOfferInfo(requestDTO, chargeType, null);

    }

    @Test
    public void findUser() {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("customerType", "互联网");
        }}));

        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        when(interactiveService.getUserInfo(userDetail)).thenReturn(new UserWrapper(retMap).getValue());

        UserInfoDto userInfoDto = orderService.findUser(userDetail);
        Assert.assertTrue(userInfoDto.isInternetAccount());

        Mockito.verify(orderService, Mockito.times(1)).findUser(userDetail);
        Mockito.verify(interactiveService, Mockito.times(1)).getUserInfo(userDetail);
    }

    @Test
    public void findUserFail() {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "-1");
        retMap.put("respDesc", "FAIL");

        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        when(interactiveService.getUserInfo(userDetail)).thenReturn(new UserWrapper(retMap).getValue());

        try {
            orderService.findUser(userDetail);
        } catch (MopException t) {
            ResponseMessage responseMessage = t.toResponse();
            Assert.assertEquals("-1", responseMessage.getCode());
            Assert.assertEquals("FAIL", responseMessage.getMessage());
        }

        Mockito.verify(orderService, Mockito.times(1)).findUser(userDetail);
        Mockito.verify(interactiveService, Mockito.times(1)).getUserInfo(userDetail);
    }

    @Test
    public void findSubscribeCode() {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("changeProcedure", "SYAN_UNHT_changeOrderPay");
            put("addProcedure", "SYAN_UNHT_createOrderPay");
        }}));

        final String payType = "1";
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        when(interactiveService.queryProcedureCode(userDetail, payType)).thenReturn(new FindSubscribeCodeWrapper(retMap));

        FindSubscribeCodeWrapper subscribeCodeWrapper = orderService.findSubscribeCode(userDetail, payType);
        FindSubscribeCodeWrapper.Result result = subscribeCodeWrapper.getValue();
        Assert.assertEquals(SubscribeCode.PREPAID.getValue(), result.getAddProcedure());

        Mockito.verify(orderService, Mockito.times(1)).findSubscribeCode(userDetail, payType);
        Mockito.verify(interactiveService, Mockito.times(1)).queryProcedureCode(userDetail, payType);

    }

    @Test
    public void findSubscribeCodeFail() {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "-1");
        retMap.put("respDesc", "FAIL");

        final String payType = "1";
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        when(interactiveService.queryProcedureCode(userDetail, payType)).thenReturn(new FindSubscribeCodeWrapper(retMap));

        try {
            FindSubscribeCodeWrapper subscribeCodeWrapper = orderService.findSubscribeCode(userDetail, payType);
        } catch (MopException t) {
            ResponseMessage responseMessage = t.toResponse();
            Assert.assertEquals("-1", responseMessage.getCode());
            Assert.assertEquals("FAIL", responseMessage.getMessage());
        }

        Mockito.verify(orderService, Mockito.times(1)).findSubscribeCode(userDetail, payType);
        Mockito.verify(interactiveService, Mockito.times(1)).queryProcedureCode(userDetail, payType);

    }

    @Test
    public void genPayLink() {
    }

    @Test
    public void subscribe() {
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());
        final SubscribeRequestDTO dto = new SubscribeRequestDTO();
        dto.setServiceId("1");
        dto.setGroupId("2");
        dto.setPeriodType("3");
        dto.setDuration(4);
        dto.setChargingMode("5");
        dto.setName("6");
//        dto.setRegion("7");
        dto.setZone("8");
        dto.setSpec(new SubscribeRequestDTO.Spec());
        dto.getSpec().setDirection("9");
        dto.getSpec().setRegion("10");
        dto.getSpec().setSpecType("11");
        dto.getSpec().setCpu(12);
        dto.getSpec().setMemory(13);
        dto.setAgentType(AgentType.Cloud.name());

        final Map<String, Object> userRetMap = new HashMap<>();
        userRetMap.put("respCode", "0");
        userRetMap.put("respDesc", "OK");
        userRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("customerType", "互联网");
        }}));
        final UserWrapper userWrapper = new UserWrapper(userRetMap);
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setCustomerType("互联网");
        doReturn(userInfoDto).when(orderService).findUser(userDetail);
        final String chargeType = userWrapper.getValue().isInternetAccount() ? "0" : "1";

        final Map<String, Object> checkRetMap = new HashMap<>();
        checkRetMap.put("respCode", "0");
        checkRetMap.put("respDesc", "OK");
        checkRetMap.put("result", new HashMap<String, Object>() {{
            put("checkInfo", "OK，校验成功");
            put("checkCode", "0");
        }});
        final CheckSubscribeWrapper checkSubscribeWrapper = new CheckSubscribeWrapper(checkRetMap);
        doReturn(checkSubscribeWrapper).when(orderService).checkSubscribe(dto, chargeType, null);

        final Map<String, Object> codeRetMap = new HashMap<>();
        codeRetMap.put("respCode", "0");
        codeRetMap.put("respDesc", "OK");
        codeRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("changeProcedure", "SYAN_UNHT_changeOrderPay");
            put("addProcedure", "SYAN_UNHT_createOrderPay");
        }}));
        final FindSubscribeCodeWrapper subscribeCodeWrapper = new FindSubscribeCodeWrapper(codeRetMap);
        doReturn(subscribeCodeWrapper).when(orderService).findSubscribeCode(userDetail, chargeType);

        final AgentDto agent = new AgentDto();
        agent.setId(new ObjectId("6012c28a7d00a59212289321"));
        ArgumentMatcher<AgentDto> agentDtoArgumentMatcher = new ArgumentMatcher<AgentDto>() {
            @Override
            public boolean matches(AgentDto argument) {
                assertEquals(dto.getName(), argument.getName());
                assertEquals(dto.getZone(), argument.getZone());
//                assertEquals(dto.getRegion(), argument.getRegion());
                assertEquals(dto.getSpec().getRegion(), argument.getSpec().getRegion());
                assertEquals(dto.getSpec().getCpu().intValue(), argument.getSpec().getCpu());
                assertEquals(dto.getSpec().getDirection(), argument.getSpec().getDirection());
                assertEquals(dto.getSpec().getMemory().intValue(), argument.getSpec().getMemory());
                assertEquals(dto.getSpec().getSpecType(), argument.getSpec().getSpecType());
                assertEquals(dto.getDuration(), argument.getOrderInfo().getDuration());
                assertEquals(dto.getChargingMode(), argument.getOrderInfo().getChargingMode());
                assertEquals(dto.getPeriodType(), argument.getOrderInfo().getPeriodType());
                assertEquals(dto.getServiceId(), argument.getOrderInfo().getServiceId());
                assertEquals(dto.getGroupId(), argument.getOrderInfo().getGroupId());
               // assertEquals(dto.getPoolId(), argument.getRegion());
                return true;
            }

            @Override
            public String toString() {
                return "[SubscribeRequestDTO eq AgentDto]";
            }
        };
        when(agentService.save(argThat(agentDtoArgumentMatcher), eq(userDetail))).thenReturn(agent);

        final String agentJson = JSON.toJSONString(Collections.singletonMap("agentId", agent.getId().toHexString()));
        final String orderId = "123";
        final Map<String, Object> subscribeRetMap = new HashMap<>();
        subscribeRetMap.put("respCode", "0");
        subscribeRetMap.put("respDesc", "OK");
        List<SubscribeWrapper.ProductItem> products = new ArrayList<>();
        SubscribeWrapper.ProductItem productItem = new SubscribeWrapper.ProductItem();
        productItem.setOrderExtId("MOP-O-8888888");
        products.add(productItem);
        subscribeRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("orderId", orderId);
            put("products", products);
        }}));
        SubscribeWrapper sw = new SubscribeWrapper(subscribeRetMap);
        doReturn(sw).when(interactiveService).subscribe(
                subscribeCodeWrapper.getValue().getSubscribeCode(),
                userWrapper.getValue().isInternetAccount(),
                userDetail,
                dto,
                agentJson
        );

        SubscribeWrapper subscribeWrapper = orderService.subscribe(dto, userDetail);
        SubscribeWrapper.Result value = subscribeWrapper.getValue();
        Assert.assertEquals(orderId, value.getOrderId());
        Assert.assertEquals(subscribeCodeWrapper.getValue().getSubscribeCode().name(), value.getPayMode());

        Mockito.verify(orderService, Mockito.times(1)).findUser(userDetail);
        Mockito.verify(orderService, Mockito.times(1)).checkSubscribe(dto, chargeType, null);
        Mockito.verify(orderService, Mockito.times(1)).findSubscribeCode(userDetail, chargeType);
        Mockito.verify(agentService, Mockito.times(1)).save(argThat(agentDtoArgumentMatcher), eq(userDetail));
        Mockito.verify(interactiveService, Mockito.times(1)).subscribe(
                subscribeCodeWrapper.getValue().getSubscribeCode(),
                userWrapper.getValue().isInternetAccount(),
                userDetail,
                dto,
                agentJson
        );

    }

    @Test
    public void changeSubscribe() throws Throwable {
        final ChangeSubscribeRequestDTO requestDTO = new ChangeSubscribeRequestDTO();
        requestDTO.setAgentId("6017b93a8c5362404278cb82");
//        requestDTO.setSpec(new ChangeSubscribeRequestDTO.Spec());
//        requestDTO.getSpec().setCpu(1);
//        requestDTO.getSpec().setMemory(2);
//        requestDTO.getSpec().setSpecType("5");
//        requestDTO.getSpec().setDirection("6");
//        requestDTO.getSpec().setRegion("7");
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        final ObjectId agentId = new ObjectId(requestDTO.getAgentId());
        final AgentDto agentDto = new AgentDto();
        agentDto.setId(new ObjectId(requestDTO.getAgentId()));
        agentDto.setStatus(AgentStatus.Running.name());
        agentDto.setOrderInfo(new OrderInfo());
        agentDto.getOrderInfo().setServiceId("1");
        agentDto.getOrderInfo().setGroupId("2");
        agentDto.getOrderInfo().setChargingMode("4");
        Spec spec = new Spec();
        spec.setVersion("1.0.0-prod");
        agentDto.setSpec(spec);
        doReturn(agentDto).when(agentService).findById(agentId, userDetail);

        final UpdateResult updateResult = UpdateResult.acknowledged(1L, 1L, new BsonObjectId(agentId));
        ArgumentMatcher<Query> queryArgumentMatcher = new ArgumentMatcher<Query>() {
            final Query query = query(where("_id").is(agentId).and("status").in(AgentStatusMachine.upstreamStatus(AgentStatus.Running, AgentStatus.WaitingAlter).stream().map(Enum::name).collect(Collectors.toSet())));

            @Override
            public boolean matches(Query argument) {
                return this.query.equals(argument);
            }
        };
        ArgumentMatcher<Update> updateArgumentMatcher = new ArgumentMatcher<Update>() {
            final Update u = update("status", AgentStatus.WaitingAlter.name());

            @Override
            public boolean matches(Update argument) {
                return u.equals(argument);
            }
        };
        doReturn(updateResult).when(agentService).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));

        final Map<String, Object> userRetMap = new HashMap<>();
        userRetMap.put("respCode", "0");
        userRetMap.put("respDesc", "OK");
        userRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("customerType", "互联网");
        }}));
        final UserWrapper userWrapper = new UserWrapper(userRetMap);
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setCustomerType("互联网");
        doReturn(userInfoDto).when(orderService).findUser(userDetail);

        ArgumentMatcher<SubscribeRequestDTO> checkArgumentMatcher = new ArgumentMatcher<SubscribeRequestDTO>() {
            @Override
            public boolean matches(SubscribeRequestDTO argument) {
//                Assert.assertEquals(requestDTO.getPoolId(), argument.getPoolId());
//                Assert.assertEquals(requestDTO.getChargingMode(), argument.getChargingMode());
//                Assert.assertEquals(requestDTO.getServiceId(), argument.getServiceId());
//                Assert.assertEquals(requestDTO.getGroupId(), argument.getGroupId());
                return true;
            }
        };
        doReturn(null).when(orderService).checkSubscribe(argThat(checkArgumentMatcher), eq("0"), null);

        final Map<String, Object> codeRetMap = new HashMap<>();
        codeRetMap.put("respCode", "0");
        codeRetMap.put("respDesc", "OK");
        codeRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("changeProcedure", "SYAN_UNHT_changeOrderPay");
            put("addProcedure", "SYAN_UNHT_createOrderPay");
        }}));
        FindSubscribeCodeWrapper findSubscribeCodeWrapper = new FindSubscribeCodeWrapper(codeRetMap);
        doReturn(findSubscribeCodeWrapper).when(orderService).findSubscribeCode(userDetail, "0");
        ChangeSubscribeCode changeSubscribeCode = findSubscribeCodeWrapper.getValue().getChangeSubscribeCode();


        ArgumentMatcher<AgentAlterDto> alterDtoArgumentMatcher = new ArgumentMatcher<AgentAlterDto>() {
            @Override
            public boolean matches(AgentAlterDto argument) {
//                Assert.assertEquals(requestDTO.getSpec().getSpecType(), argument.getSpec().getSpecType());
//                Assert.assertEquals(requestDTO.getSpec().getDirection(), argument.getSpec().getDirection());
//                Assert.assertEquals(requestDTO.getSpec().getRegion(), argument.getSpec().getRegion());
//                Assert.assertEquals(requestDTO.getSpec().getCpu().intValue(), argument.getSpec().getCpu());
//                Assert.assertEquals(requestDTO.getSpec().getMemory().intValue(), argument.getSpec().getMemory());
//                Assert.assertEquals(AgentAlterStatus.Waiting.name(), argument.getStatus());
                return true;
            }
        };
        final AgentAlterDto alterDto = new AgentAlterDto();
        alterDto.setId(new ObjectId("601a603b8c5362404278cb83"));
        doReturn(alterDto).when(agentAlterService).save(argThat(alterDtoArgumentMatcher), refEq(userDetail));

        final String chagneParamJson = JSON.toJSONString(Collections.singletonMap(CallbackParamName.CHANGE_RECORD_ID, alterDto.getId().toHexString()));
        final String orderId = "MOP-O-**************";
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        List<ChangeSubscribeWrapper.ProductItem> products = new ArrayList<>();
        ChangeSubscribeWrapper.ProductItem productItem = new ChangeSubscribeWrapper.ProductItem();
        productItem.setOrderExtId("MOP-O-8888888");
        products.add(productItem);
        retMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {
            {
                put("orderId", orderId);
                put("products", products);
            }
        }));
        final ChangeSubscribeWrapper changeSubscribeWrapper = new ChangeSubscribeWrapper(retMap);
        doReturn(changeSubscribeWrapper).when(interactiveService).changeSubscribe(changeSubscribeCode, requestDTO.getAgentId(), requestDTO.getAgentId(), userWrapper.getValue().isInternetAccount(), userDetail, requestDTO, chagneParamJson);

        ChangeSubscribeWrapper resultWraper = orderService.changeSubscribe(requestDTO, userDetail);
        ChangeSubscribeWrapper.Result value = resultWraper.getValue();
        Assert.assertEquals(orderId, value.getOrderId());
        Assert.assertEquals(changeSubscribeCode.name(), value.getPayMode());

        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
        Mockito.verify(agentService, Mockito.times(1)).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));
        Mockito.verify(orderService, Mockito.times(1)).findUser(userDetail);
        Mockito.verify(orderService, Mockito.times(1)).checkSubscribe(argThat(checkArgumentMatcher), eq("0"), null);
        Mockito.verify(orderService, Mockito.times(1)).findSubscribeCode(userDetail, "0");
        Mockito.verify(agentAlterService, Mockito.times(1)).save(argThat(alterDtoArgumentMatcher), refEq(userDetail));
        Mockito.verify(interactiveService, Mockito.times(1)).changeSubscribe(changeSubscribeCode, requestDTO.getAgentId(), requestDTO.getAgentId(), userWrapper.getValue().isInternetAccount(), userDetail, requestDTO, chagneParamJson);
        PowerMockito.verifyPrivate(orderService).invoke("doSubscribe", eq(agentDto), eq(AgentStatus.WaitingAlter), refEq(userDetail), notNull());

    }

    @Test
    public void unSubscribe() throws Throwable {
        final UnSubscribeRequestDTO requestDTO = new UnSubscribeRequestDTO();
        requestDTO.setInstanceId("6017b93a8c5362404278cb82");
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        final ObjectId agentId = new ObjectId(requestDTO.getInstanceId());
        final AgentDto agentDto = new AgentDto();
        agentDto.setStatus(AgentStatus.Running.name());
        doReturn(agentDto).when(agentService).findById(agentId, userDetail);

        final UpdateResult updateResult = UpdateResult.acknowledged(1L, 1L, new BsonObjectId(agentId));
        ArgumentMatcher<Query> queryArgumentMatcher = new ArgumentMatcher<Query>() {
            final Query query = query(where("_id").is(agentId).and("status").in(AgentStatusMachine.upstreamStatus(AgentStatus.Running, AgentStatus.WaitingDelete).stream().map(Enum::name).collect(Collectors.toSet())));

            @Override
            public boolean matches(Query argument) {
                return this.query.equals(argument);
            }
        };
        ArgumentMatcher<Update> updateArgumentMatcher = new ArgumentMatcher<Update>() {
            final Update u = update("status", AgentStatus.WaitingDelete.name());

            @Override
            public boolean matches(Update argument) {
                return u.equals(argument);
            }
        };
        doReturn(updateResult).when(agentService).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));

        final String orderId = "MOP-O-**************";
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {
            {
                put("orderId", orderId);
            }
        }));
        doReturn(retMap).when(interactiveService).unSubscribe(requestDTO, userDetail);

        RawMopResultWrapper resultWraper = orderService.unSubscribe(requestDTO, userDetail);
        Map<String, Object> result = (Map<String, Object>) resultWraper.getValue();
        Assert.assertEquals(orderId, result.get("orderId"));

        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
        Mockito.verify(agentService, Mockito.times(1)).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));
        Mockito.verify(orderService, Mockito.times(1)).unSubscribe(requestDTO, userDetail);
        Mockito.verify(interactiveService, Mockito.times(1)).unSubscribe(requestDTO, userDetail);
        PowerMockito.verifyPrivate(orderService).invoke("doSubscribe", eq(requestDTO.getInstanceId()), eq(AgentStatus.WaitingDelete), refEq(userDetail), notNull());
    }

    @Test
    public void renewSubscribe() {
        final RenewSubscribeRequestDTO requestDTO = new RenewSubscribeRequestDTO();
        requestDTO.setAgentId("");
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());

        final Map<String, Object> userRetMap = new HashMap<>();
        userRetMap.put("respCode", "0");
        userRetMap.put("respDesc", "OK");
        userRetMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {{
            put("customerType", "互联网");
        }}));
        final UserWrapper userWrapper = new UserWrapper(userRetMap);
        doReturn(userWrapper).when(orderService).findUser(userDetail);

        final String orderId = "MOP-O-**************";
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("respCode", "0");
        retMap.put("respDesc", "OK");
        retMap.put("result", Collections.singletonMap("body", new HashMap<String, Object>() {
            {
                put("orderId", orderId);
            }
        }));
        doReturn(retMap).when(interactiveService).renewSubscribe(userWrapper.getValue().isInternetAccount(), userDetail, requestDTO);

        RenewSubscribeWrapper renewSubscribeWrapper = orderService.renewSubscribe(requestDTO, userDetail);
        Assert.assertEquals(orderId, renewSubscribeWrapper.getValue().getOrderId());
        Assert.assertEquals(SubscribeCode.PREPAID.name(), renewSubscribeWrapper.getValue().getPayMode());

        Mockito.verify(orderService, Mockito.times(1)).findUser(userDetail);
        Mockito.verify(interactiveService, Mockito.times(1)).renewSubscribe(userWrapper.getValue().isInternetAccount(), userDetail, requestDTO);
        Mockito.verify(orderService, Mockito.times(1)).renewSubscribe(requestDTO, userDetail);
    }

    @Test
    public void doSubscribeAgentNotExists() {
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());
        final String agentIdHex = "6017b93a8c5362404278cb82";
        final ObjectId agentId = new ObjectId(agentIdHex);
        doReturn(null).when(agentService).findById(agentId, userDetail);
        try {
            Method method = PowerMockito.method(OrderService.class, "doSubscribe", String.class, AgentStatus.class, UserDetail.class, Supplier.class);
            method.invoke(orderService, agentIdHex, AgentStatus.WaitingDelete, userDetail, new Supplier<Object>() {
                @Override
                public Object get() {
                    return null;
                }
            });
        } catch (Throwable e) {
            Assert.assertTrue((e.getCause()) instanceof AgentNotExistsException);
        }
        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
    }

    @Test
    public void doSubscribeNoUpstreamStatus() {
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());
        final String agentIdHex = "6017b93a8c5362404278cb82";

        final ObjectId agentId = new ObjectId(agentIdHex);
        final AgentDto agentDto = new AgentDto();
        agentDto.setId(agentId);
        agentDto.setStatus(AgentStatus.Running.name());
        doReturn(agentDto).when(agentService).findById(agentId, userDetail);

        try {
            Method method = PowerMockito.method(OrderService.class, "doSubscribe", String.class, AgentStatus.class, UserDetail.class, Supplier.class);
            method.invoke(orderService, agentIdHex, AgentStatus.Running, userDetail, new Supplier<Object>() {
                @Override
                public Object get() {
                    return null;
                }
            });
        } catch (Throwable e) {
            Assert.assertTrue((e.getCause()) instanceof InvalidAgentStatusException);
        }
        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
    }

    @Test
    public void doSubscribeAgentStatusUpdateFail() {
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());
        final String agentIdHex = "6017b93a8c5362404278cb82";

        final ObjectId agentId = new ObjectId(agentIdHex);
        final AgentDto agentDto = new AgentDto();
        agentDto.setId(agentId);
        agentDto.setStatus(AgentStatus.Running.name());
        doReturn(agentDto).when(agentService).findById(agentId, userDetail);

        UpdateResult updateResult = UpdateResult.unacknowledged();
        ArgumentMatcher<Query> queryArgumentMatcher = new ArgumentMatcher<Query>() {
            final Query query = query(where("_id").is(agentId).and("status").in(AgentStatusMachine.upstreamStatus(AgentStatus.Running, AgentStatus.WaitingDelete).stream().map(Enum::name).collect(Collectors.toSet())));

            @Override
            public boolean matches(Query argument) {
                return this.query.equals(argument);
            }
        };
        ArgumentMatcher<Update> updateArgumentMatcher = new ArgumentMatcher<Update>() {
            final Update u = update("status", AgentStatus.WaitingDelete.name());

            @Override
            public boolean matches(Update argument) {
                return u.equals(argument);
            }
        };
        doReturn(updateResult).when(agentService).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));

        try {
            Method method = PowerMockito.method(OrderService.class, "doSubscribe", String.class, AgentStatus.class, UserDetail.class, Supplier.class);
            method.invoke(orderService, agentIdHex, AgentStatus.WaitingDelete, userDetail, new Supplier<Object>() {
                @Override
                public Object get() {
                    return null;
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
            Assert.assertTrue((e.getCause()) instanceof InvalidAgentStatusException);
        }
        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
        Mockito.verify(agentService, Mockito.times(1)).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));
    }

    @Test
    public void doSubscribeRollbackStatus() {
        final UserDetail userDetail = new UserDetail("1", "1", "1", "1", Collections.emptyList());
        final String agentIdHex = "6017b93a8c5362404278cb82";

        final ObjectId agentId = new ObjectId(agentIdHex);
        final AgentDto agentDto = new AgentDto();
        agentDto.setId(agentId);
        agentDto.setStatus(AgentStatus.Running.name());
        doReturn(agentDto).when(agentService).findById(agentId, userDetail);

        UpdateResult updateResult = UpdateResult.acknowledged(1L, 1L, new BsonObjectId(agentId));
        ArgumentMatcher<Query> queryArgumentMatcher = new ArgumentMatcher<Query>() {
            int count = 2;
            final Query query = query(where("_id").is(agentId).and("status").in(AgentStatusMachine.upstreamStatus(AgentStatus.Running, AgentStatus.WaitingDelete).stream().map(Enum::name).collect(Collectors.toSet())));
            final Query rollbackQuery = query(where("_id").is(agentId).and("status").is(AgentStatus.WaitingDelete.name()));

            @Override
            public boolean matches(Query argument) {
                count--;
                try {
                    switch (count) {
                        case 1:
                            return this.query.equals(argument);
                        case 0:
                            return this.rollbackQuery.equals(argument);
                    }
                } finally {
                    if (count == 0) {
                        count = 2;
                    }
                }
                return false;
            }
        };
        ArgumentMatcher<Update> updateArgumentMatcher = new ArgumentMatcher<Update>() {
            int count = 2;
            final Update u = update("status", AgentStatus.WaitingDelete.name());
            final Update rollbackU = update("status", AgentStatus.Running.name());

            @Override
            public boolean matches(Update argument) {
                count--;
                try {
                    switch (count) {
                        case 1:
                            return this.u.equals(argument);
                        case 0:
                            return this.rollbackU.equals(argument);
                    }
                } finally {
                    if (count == 0) {
                        count = 2;
                    }
                }
                return false;
            }
        };

        doReturn(updateResult).when(agentService).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));

        try {
            Method method = PowerMockito.method(OrderService.class, "doSubscribe", String.class, AgentStatus.class, UserDetail.class, Supplier.class);
            method.invoke(orderService, agentIdHex, AgentStatus.WaitingDelete, userDetail, new Supplier<Object>() {
                @Override
                public Object get() {
                    throw new MopException("");
                }
            });
        } catch (Throwable e) {
            Assert.assertTrue((e.getCause()) instanceof MopException);
        }
        Mockito.verify(agentService, Mockito.times(1)).findById(agentId, userDetail);
        Mockito.verify(agentService, Mockito.times(2)).update(argThat(queryArgumentMatcher), argThat(updateArgumentMatcher), refEq(userDetail));
    }

}
