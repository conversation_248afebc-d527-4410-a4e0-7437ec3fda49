package com.tapdata.tm.activelock;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 集群活动节点单例锁-配置
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/8/7 00:30 Create
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "application.active-lock")
public class ActiveLockConfig {
    private final ThreadGroup threadGroup;
    private final AtomicInteger idAtomic;
    @Getter
    private final ScheduledExecutorService executor;

    public ActiveLockConfig() {
        threadGroup = new ThreadGroup(ActiveLock.TAG);
        idAtomic = new AtomicInteger(0);
        executor = Executors.newScheduledThreadPool(Integer.MAX_VALUE, r -> {
            String threadName = String.format("%s-%s", ActiveLock.TAG, idAtomic.getAndIncrement());
            return new Thread(threadGroup, r, threadName);
        });
    }

    @Setter
    @Getter
    private int expireSeconds = 30;
    @Setter
    @Getter
    private int heartbeatSeconds = 10;
    @Setter
    @Getter
    private int checkIntervalSeconds = 10;
    @Setter
    @Getter
    private String serverId = UUID.randomUUID().toString();
    @Setter
    @Getter
    private String serverName = SystemUtils.getHostName();

    @PreDestroy
    public void onDestroy() {
        log.info("{} destroy...", ActiveLock.TAG);
        executor.shutdown();
    }
}
