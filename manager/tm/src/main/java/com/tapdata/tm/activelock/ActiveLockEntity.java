package com.tapdata.tm.activelock;

import com.tapdata.tm.base.entity.Entity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 集群活动锁-实体
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/8/7 14:36 Create
 */
@Setter
@Getter
@Document("ActiveLock")
@EqualsAndHashCode(callSuper = true)
public class ActiveLockEntity extends Entity {
    public static final  String FIELD_NAME = "name";
    public static final  String FIELD_SERVER_ID = "serverId";
    public static final  String FIELD_SERVERNAME = "serverName";
    public static final  String FIELD_CREATED = "created";
    public static final  String FIELD_UPDATED = "updated";
    public static final  String FIELD_EXPIRED = "expired";

    private String name;       // 名称
    private String serverId;   // 服务编号
    private String serverName; // 服务名
    private Date created;      // 创建时间
    private Date updated;      // 更新时间
    private Date expired;      // 到期时间
}
