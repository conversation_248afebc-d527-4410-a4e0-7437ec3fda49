package com.tapdata.tm.activelock;

import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 集群活动锁-数据访问层
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2025/8/7 14:35 Create
 */
@Slf4j
@Repository
public class ActiveLockRepository {

    private final static Class<ActiveLockEntity> entityClass = ActiveLockEntity.class;
    private final MongoTemplate operations;

    public ActiveLockRepository(MongoTemplate operations) {
        this.operations = operations;

        if (!operations.collectionExists(entityClass)) {
            operations.createCollection(entityClass);
            operations.indexOps(entityClass).createIndex(new Index(ActiveLockEntity.FIELD_NAME, Sort.Direction.ASC).unique());
            operations.indexOps(entityClass).createIndex(new Index(ActiveLockEntity.FIELD_EXPIRED, Sort.Direction.ASC).expire(0));
        }
    }

    /**
     * 更新心跳
     *
     * @param name          锁名称
     * @param serverId      服务编号
     * @param serverName    服务名
     * @param expireSeconds 过期时间
     * @return true: 更新成功，false: 更新失败
     */
    public boolean updateHeartbeat(String name, String serverId, String serverName, int expireSeconds) {
        Date currentDate = new Date();
        Date expireDate = new Date(currentDate.getTime() - expireSeconds * 1000L);
        Query query = Query.query(Criteria
            .where(ActiveLockEntity.FIELD_NAME).is(name)
            .and(ActiveLockEntity.FIELD_SERVER_ID).is(serverId)
        );

        Update update = Update.update(ActiveLockEntity.FIELD_UPDATED, currentDate)
            .set(ActiveLockEntity.FIELD_EXPIRED, expireDate);

        UpdateResult updateResult = operations.updateFirst(query, update, entityClass);
        return updateResult.getModifiedCount() > 0;
    }

    /**
     * 尝试获取锁
     *
     * @param name          锁名称
     * @param serverId      服务编号
     * @param serverName    服务名
     * @param expireSeconds 过期时间
     * @return true: 获取成功，false: 获取失败
     */
    public boolean tryLock(String name, String serverId, String serverName, int expireSeconds) {
        Date currentDate = new Date();
        Date expireDate = new Date(currentDate.getTime() - expireSeconds * 1000L);

        Query query = Query.query(Criteria.where(ActiveLockEntity.FIELD_NAME).is(name));

        Update update = Update.update(ActiveLockEntity.FIELD_SERVER_ID, serverId)
            .set(ActiveLockEntity.FIELD_UPDATED, currentDate)
            .set(ActiveLockEntity.FIELD_EXPIRED, expireDate);

        FindAndModifyOptions options = FindAndModifyOptions.options().returnNew(true).upsert(true);
        ActiveLockEntity entity = operations.findAndModify(query, update, options, entityClass);
        return null != newEntity;
    }

}
