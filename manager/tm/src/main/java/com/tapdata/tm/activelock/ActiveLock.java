package com.tapdata.tm.activelock;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 集群活动节点单例锁
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/8/7 00:30 Create
 */
@Slf4j
public abstract class ActiveLock {
    public static final String TAG = ActiveLock.class.getSimpleName();
    private static final Set<String> activeLocks = new HashSet<>();

    protected final ActiveLockConfig config;
    protected final ActiveLockRepository repository;

    @Getter
    private final String name;
    @Getter
    private boolean active;
    private ScheduledFuture<?> operationFuture;

    public ActiveLock(String name, ActiveLockConfig config, ActiveLockRepository repository) {
        // 防止服务锁重复定义
        synchronized (activeLocks) {
            if (activeLocks.contains(name)) {
                throw new IllegalArgumentException("Active lock " + name + " already exists");
            }
            activeLocks.add(name);
        }

        this.name = name;
        this.config = config;
        this.repository = repository;
        config.getExecutor().scheduleAtFixedRate(this::heartbeat, 5, config.getHeartbeatSeconds(), TimeUnit.SECONDS);
    }

    private void heartbeat() {
        try {
            if (active) {
                if (!repository.updateHeartbeat(getName(), config.getServerId(), config.getServerName(), config.getExpireSeconds())) {
                    log.warn("{} heartbeat failed, switch to standby", TAG);
                    active = false;
                    doStandby();
                    operationFuture.cancel(true);
                }
            } else if (repository.tryLock(getName(), config.getServerId(), config.getServerName(), config.getExpireSeconds())) {
                log.info("{} try lock success, switch to active", TAG);
                active = true;
                doActive();
                operationFuture = config.getExecutor().scheduleAtFixedRate(this::doOperation, 5, 1, TimeUnit.SECONDS);
            }
            log.debug("{} heartbeat current active: {}", TAG, active);
        } catch (Throwable e) {
            log.error("{} heartbeat error", TAG, e);
        }
    }

    protected abstract void doActive();

    protected abstract void doStandby();

    protected abstract void doOperation();
}
