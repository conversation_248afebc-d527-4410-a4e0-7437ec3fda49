package com.tapdata.tm.activelock.impl;

import com.tapdata.tm.activelock.ActiveLock;
import com.tapdata.tm.activelock.ActiveLockConfig;
import com.tapdata.tm.activelock.ActiveLockRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TestActiveLock extends ActiveLock {

    public TestActiveLock(ActiveLockConfig config, ActiveLockRepository repository) {
        super("test", config, repository);
    }

    @Override
    protected void doActive() {
        log.info("active do init");
    }

    @Override
    protected void doStandby() {
        log.info("standby do destroy");
    }

    @Override
    protected void doOperation() {
        log.info("operation do each");
    }
}
