<div class="lists row">
  <div class="col-md-12 col-lg-6 col-xxxl-6">
    <nb-card class="list-card">
      <nb-card-header>Some Fruits</nb-card-header>
      <nb-card-body>
        <nb-list>
          <nb-list-item *ngFor="let fruit of fruits">
            {{ fruit }}
          </nb-list-item>
        </nb-list>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="col-md-12 col-lg-6 col-xxxl-6">
    <nb-card class="list-card" size="small">
      <nb-card-header>Users</nb-card-header>
      <nb-list>
        <nb-list-item *ngFor="let user of users">
          <nb-user [name]="user.name" [title]="user.title">
          </nb-user>
        </nb-list-item>
      </nb-list>
    </nb-card>
  </div>
</div>
