import { Component } from '@angular/core';
import { UserData } from '../../../@core/data/users';
import { Router } from '@angular/router';
import { HttpErrorResponse} from '@angular/common/http';
import { NbDialogService } from '@nebular/theme';
import { ShowcaseDialogComponent } from '../../modal-overlays/dialog/showcase-dialog/showcase-dialog.component';
import { DialogNamePromptComponent } from '../../modal-overlays/dialog/dialog-name-prompt/dialog-name-prompt.component';

@Component({
  selector: 'ngx-form-layouts',
  styleUrls: ['./form-login.component.scss'],
  templateUrl: './form-login.component.html',
})
export class FormLoginComponent {
  user: any
  constructor(
    private userService: UserData,
    private router: Router,
    private dialogService: NbDialogService
  ) {
  }

  login(uid: string, password: string) {
    this.userService.login(uid, password)
      .subscribe((user: any) => {
          this.user = user;
          this.router.navigateByUrl('/pages/forms/layouts');
      }, (error: HttpErrorResponse) => {
          if (error.status == 401) {
            this.dialogService.open(ShowcaseDialogComponent, {
              context: {
                title: '登录失败',
                license: '您目前暂时没有授权访问 License 申请平台'
              },
            });
            this.router.navigateByUrl('/pages/forms/login')
          }
      });
  }
}
