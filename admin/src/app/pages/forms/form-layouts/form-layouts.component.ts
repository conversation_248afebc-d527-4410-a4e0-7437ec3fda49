import { Component } from '@angular/core';
import {Router} from '@angular/router';
import { LicenseData, License } from '../../../@core/data/licenses';
import { HttpErrorResponse} from '@angular/common/http';
import { NbDialogService } from '@nebular/theme';
import { ShowcaseDialogComponent } from '../../modal-overlays/dialog/showcase-dialog/showcase-dialog.component';
import { DialogNamePromptComponent } from '../../modal-overlays/dialog/dialog-name-prompt/dialog-name-prompt.component';


@Component({
  selector: 'ngx-form-layouts',
  styleUrls: ['./form-layouts.component.scss'],
  templateUrl: './form-layouts.component.html',
})
export class FormLayoutsComponent {
  constructor(
    private licenseService: LicenseData,
    private router: Router,
    private dialogService: NbDialogService) {
  }

  license = {} as License;

  createLicense() {
      this.licenseService.createLicense(this.license)
      .subscribe((data: any) => {
        this.dialogService.open(ShowcaseDialogComponent, {
          context: {
            title: 'License 申请完成, 请将内容粘贴到 license.txt 中, 并重启服务生效',
            sid: "sid: " + data["data"]["sid"],
            license: data["data"]["content"]
          },
        });
      }, (error: HttpErrorResponse) => {
      });
  }
}
