<div class="row">
  <div class="col-md-8">
    <nb-card>
      <nb-card-header>License 申请表单</nb-card-header>
      <nb-card-body>
        <form>
          <div class="form-group row">
            <label for="inputEmail1" class="label col-sm-3 col-form-label">申请客户</label>
            <div class="col-sm-9">
              <input type="text" nbInput fullWidth name="customer" placeholder="" [(ngModel)]="license.customer">
            </div>
          </div>
          <div class="form-group row">
            <label for="inputEmail1" class="label col-sm-3 col-form-label">申请理由</label>
            <div class="col-sm-9">
              <input type="text" nbInput fullWidth name="reason" placeholder="" [(ngModel)]="license.reason">
            </div>
          </div>
          <div class="form-group row">
            <label for="inputPassword2" class="label col-sm-3 col-form-label">有效时间天数</label>
            <div class="col-sm-9">
              <input type="text" nbInput fullWidth name="valid_days" placeholder="" [(ngModel)]="license.valid_days">
            </div>
          </div>
          <div class="form-group row">
            <label for="inputPassword2" class="label col-sm-3 col-form-label">软件版本</label>
            <div class="col-sm-9">
              <input type="text" nbInput fullWidth name="version" placeholder="" [(ngModel)]="license.version">
            </div>
          </div>
          <div class="form-group row">
            <label for="inputEmail1" class="label col-sm-3 col-form-label">sid</label>
            <div class="col-sm-9">
              <input type="text" nbInput fullWidth name="sid" placeholder="" [(ngModel)]="license.sid">
            </div>
          </div>
          <div class="form-group row">
            <div class="offset-sm-3 col-sm-9">
              <button type="submit" nbButton status="primary" (click)="createLicense()">申请 License</button>
            </div>
          </div>
        </form>
      </nb-card-body>
    </nb-card>
  </div>
</div>
