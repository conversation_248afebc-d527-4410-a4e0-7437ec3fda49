import { Component, Input, OnInit } from '@angular/core';
import { LicenseData } from '../../../@core/data/licenses';
import { NbDialogService } from '@nebular/theme';
import { ShowcaseDialogComponent } from '../../modal-overlays/dialog/showcase-dialog/showcase-dialog.component';
import { DialogNamePromptComponent } from '../../modal-overlays/dialog/dialog-name-prompt/dialog-name-prompt.component';

@Component({
  selector: 'my-name-component',
  template: `<a href="#" class="license_id" (click)="showLicense()">{{_id}}</a>`,
  styleUrls: ['./license.component.scss'],
})
export class LicenseIdComponent implements OnInit {
  @Input() value;
  _id: string;
  sid: string;
  constructor(private service: LicenseData, private dialogService: NbDialogService) { }

  ngOnInit() {
    console.log(this.value)
    this._id = this.value._id;
    this.sid = this.value.sid;
  }    

  showLicense() {
    this.service.getLicense(this._id)
    .subscribe((data: any) => {
      this.dialogService.open(ShowcaseDialogComponent, {
        context: {
          title: 'License 信息如下:',
          sid: "sid:" + this.sid,
          license: data["data"]["content"]
        },
      });
    }, (error: any) => {
    });
    return false;
  }
}
