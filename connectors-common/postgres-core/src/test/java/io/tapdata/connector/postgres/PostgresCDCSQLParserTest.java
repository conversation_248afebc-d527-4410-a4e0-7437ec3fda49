package io.tapdata.connector.postgres;

import io.tapdata.connector.postgres.cdc.PostgresCDCSQLParser;
import org.junit.jupiter.api.Test;

public class PostgresCDCSQLParserTest {

    @Test
    void testLargeInsert() {
        PostgresCDCSQLParser parser = new PostgresCDCSQLParser();
        parser.from("INSERT INTO public.pg_all_type(a1 ,a2 ,a3 ,a4 ,a5 ,a6 ,a7 ,a8 ,a9 ,a10 ,a11 ,a12 ,a13 ,a14 ,a15 ,a16 ,a17 ,a18 ,a19 ,a20 ,a21 ,a22 ,a23 ,a24 ,a25 ,a26 ,a27 ,a28 ,a29 ,a30) VALUES(2 ,1 ,1 ,2.2 ,2.2 ,2.2 ,'a' ,'a' ,'a' ,'\\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' ,B'0' ,B'1' ,true ,'2024-08-20' ,'00:20:34' ,'2024-08-20 14:39:30' ,'2024-08-20 22:39:33.449+08' ,'(1,2)' ,'{1,-1,1}' ,'[(1,2),(3,4)]' ,'(3,4),(1,2)' ,'((1,2),(3,4))' ,'((1,2),(3,4))' ,'<(1.2,1.2),1.2>' ,'127.0.0.1/32' ,'10.0.0.1' ,'12:12:12:12:11:12' ,'76a737e7-e3df-4431-bf8f-893c9c68d7d7' ,'<attribute-set xmlns=\"http://www.w3.org/1999/XSL/Transform\" name=\"df\"></attribute-set>' ,'{\"game\": 123, \"didi\": 234}')", false);
    }

    @Test
    void testInvalidUpdate() {
        PostgresCDCSQLParser parser = new PostgresCDCSQLParser();
        parser.from("UPDATE public.pg_all_type SET  WHERE a1=4", false);
    }
}
