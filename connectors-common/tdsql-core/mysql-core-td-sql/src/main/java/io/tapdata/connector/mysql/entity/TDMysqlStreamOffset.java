package io.tapdata.connector.mysql.entity;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-05-05 14:48
 **/
public class TDMysqlStreamOffset extends MysqlStreamOffset implements Serializable {

    private static final long serialVersionUID = 7107575040120294790L;

    private final Map<String, Map<String, String>> offsetMap = new HashMap<>();

    public void setName(String name) {
        offsetMap.put(name, new HashMap<>());
        this.name = name;
    }

    public Map<String, Map<String, String>> getOffsetMap() {
        return offsetMap;
    }

    public void setOffsetMap(String name, Map<String, String> offsetMap) {
        this.offsetMap.put(name, offsetMap);
    }

    public Map<String, String> getOffset(String setName) {
        return offsetMap.get(setName);
    }

    @Override
    public String toString() {
        return "TDMysqlStreamOffset{" +
                "name='" + name + '\'' +
                ", offsetMap=" + offsetMap +
                '}';
    }
}
