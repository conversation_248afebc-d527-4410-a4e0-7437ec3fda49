<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.tapdata</groupId>
        <artifactId>connectors-common</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>js-connector-core-plus</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>js-connector-core-plus</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>8</java.version>
        <tapdata.pdk.api.version>2.0.0-SNAPSHOT</tapdata.pdk.api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-pdk-api</artifactId>
            <version>${tapdata.pdk.api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-api</artifactId>
            <scope>provided</scope>
            <version>${tapdata.pdk.api.version}</version>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>js-connector-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.js</include>
                </includes>
                <targetPath>./</targetPath>
            </resource>
        </resources>
    </build>
</project>
