package com.tapdata.taskinspect;

import com.tapdata.entity.TapdataRecoveryEvent;
import com.tapdata.pdk.IPdkConnector;
import com.tapdata.pdk.TaskPdkConnector;
import com.tapdata.taskinspect.utils.Compare;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.taskinspect.cons.TimeCheckModeEnum;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableMappingException;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import com.tapdata.tm.vo.TaskNodeTableFieldTraceVo;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.inspect.AutoRecoveryClient;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.RoundingMode;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("TaskInspectChecker Class Test")
class TaskInspectCheckerTest {

    @Mock
    private TaskInspectContext mockContext;

    @Mock
    private ICheckQueue mockCheckQueue;

    @Mock
    private TaskPdkConnector mockTaskPdkConnector;

    @Mock
    private IPdkConnector mockSourceConnector;

    @Mock
    private IPdkConnector mockTargetConnector;

    @Mock
    private AutoRecoveryClient mockAutoRecoveryClient;

    @Mock
    private IResultReporter mockResultReporter;

    @Mock
    private Compare mockCompare;

    private TaskInspectChecker taskInspectChecker;

    private Map<String, TaskNodeTableFieldTraceVo> mockTableFieldTrace;

    @BeforeEach
    void setUp() {
        reset(mockContext, mockCheckQueue, mockTaskPdkConnector, mockSourceConnector,
              mockTargetConnector, mockAutoRecoveryClient, mockResultReporter, mockCompare);

        lenient().when(mockContext.getIgnoreTimePrecision()).thenReturn(false);
        lenient().when(mockContext.getRoundingMode()).thenReturn(RoundingMode.HALF_UP.name());
        TaskDto taskDto = new TaskDto();
        taskDto.setId(new ObjectId());
        lenient().when(mockContext.getTask()).thenReturn(taskDto);

        taskInspectChecker = new TaskInspectChecker(mockContext, mockCheckQueue);

        try {
            java.lang.reflect.Field compareField = TaskInspectChecker.class.getDeclaredField("compare");
            compareField.setAccessible(true);
            compareField.set(taskInspectChecker, mockCompare);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup test fields", e);
        }
    }

    private void setupSendCdcRecoverMocks() {
        mockTableFieldTrace = new HashMap<>();
        TaskNodeTableFieldTraceVo mockFieldTrace = mock(TaskNodeTableFieldTraceVo.class);
        when(mockFieldTrace.getSourceFields()).thenReturn(Arrays.asList("id", "name", "value"));
        when(mockFieldTrace.getTargetTable()).thenReturn("target_table");
        when(mockFieldTrace.getTargetFields()).thenReturn(Arrays.asList("id", "name", "value"));

        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put("id", "id");
        fieldMap.put("name", "name");
        fieldMap.put("value", "value");
        when(mockFieldTrace.getFieldMap()).thenReturn(fieldMap);

        mockTableFieldTrace.put("test_table", mockFieldTrace);

        try {
            java.lang.reflect.Field tableFieldTraceField = TaskInspectChecker.class.getDeclaredField("tableFieldTrace");
            tableFieldTraceField.setAccessible(true);
            tableFieldTraceField.set(taskInspectChecker, mockTableFieldTrace);

            java.lang.reflect.Field sourceConnectorField = TaskInspectChecker.class.getDeclaredField("sourceConnector");
            sourceConnectorField.setAccessible(true);
            sourceConnectorField.set(taskInspectChecker, mockSourceConnector);

            java.lang.reflect.Field targetConnectorField = TaskInspectChecker.class.getDeclaredField("targetConnector");
            targetConnectorField.setAccessible(true);
            targetConnectorField.set(taskInspectChecker, mockTargetConnector);

            java.lang.reflect.Field autoRecoveryClientField = TaskInspectChecker.class.getDeclaredField("autoRecoveryClient");
            autoRecoveryClientField.setAccessible(true);
            autoRecoveryClientField.set(taskInspectChecker, mockAutoRecoveryClient);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup test fields", e);
        }
    }

    @Nested
    class SendCdcRecoverTest {

        private String testRowId = "test-row-id";
        private String testTableName = "test_table";
        private LinkedHashMap<String, Object> testKeys;

        @BeforeEach
        void setUp() {
            testKeys = new LinkedHashMap<>();
            testKeys.put("id", 1);
            testKeys.put("name", "test");
        }

        @Test
        void testSendCdcRecover_SourceRecordExists_ShouldEnqueueInsertEvent() throws Exception {
            setupSendCdcRecoverMocks();
            when(mockContext.getTaskId()).thenReturn("test-task-id");
            when(mockContext.getUserId()).thenReturn("test-user-id");
            when(mockContext.getUserName()).thenReturn("test-user-name");
            when(mockContext.getResultReporter()).thenReturn(mockResultReporter);

            LinkedHashMap<String, Object> sourceRecord = new LinkedHashMap<>();
            sourceRecord.put("id", 1);
            sourceRecord.put("name", "test");
            sourceRecord.put("value", "test_value");

            when(mockSourceConnector.findOneByKeys(eq(testTableName), eq(testKeys), anyList()))
                .thenReturn(sourceRecord);

            taskInspectChecker.sendCdcRecover(testRowId, testTableName, testKeys);

            verify(mockResultReporter).reportOperation(
                eq(testRowId),
                eq(ResultOperationsVo.OP_AUTO_RECOVER),
                eq("test-user-id"),
                eq("test-user-name"),
                eq("")
            );

            ArgumentCaptor<TapdataRecoveryEvent> eventCaptor = ArgumentCaptor.forClass(TapdataRecoveryEvent.class);
            verify(mockAutoRecoveryClient).enqueue(eventCaptor.capture());

            TapdataRecoveryEvent capturedEvent = eventCaptor.getValue();
            assertEquals(TapdataRecoveryEvent.RECOVERY_TYPE_DATA, capturedEvent.getRecoveryType());
            assertEquals(testRowId, capturedEvent.getRowId());
            assertFalse(capturedEvent.getIsManual());
            assertNotNull(capturedEvent.getTapEvent());
            assertTrue(capturedEvent.getTapEvent() instanceof TapInsertRecordEvent);
        }

        @Test
        void testSendCdcRecover_SourceRecordNull_TargetRecordExists_ShouldEnqueueDeleteEvent() throws Exception {
            setupSendCdcRecoverMocks();
            when(mockContext.getTaskId()).thenReturn("test-task-id");
            when(mockContext.getUserId()).thenReturn("test-user-id");
            when(mockContext.getUserName()).thenReturn("test-user-name");
            when(mockContext.getResultReporter()).thenReturn(mockResultReporter);

            LinkedHashMap<String, Object> targetRecord = new LinkedHashMap<>();
            targetRecord.put("id", 1);
            targetRecord.put("name", "test");
            targetRecord.put("value", "target_value");

            when(mockSourceConnector.findOneByKeys(eq(testTableName), eq(testKeys), anyList()))
                .thenReturn(null);
            when(mockTargetConnector.findOneByKeys(eq("target_table"), eq(testKeys), anyList()))
                .thenReturn(targetRecord);

            taskInspectChecker.sendCdcRecover(testRowId, testTableName, testKeys);

            verify(mockResultReporter).reportOperation(
                eq(testRowId),
                eq(ResultOperationsVo.OP_AUTO_RECOVER),
                eq("test-user-id"),
                eq("test-user-name"),
                eq("")
            );

            ArgumentCaptor<TapdataRecoveryEvent> eventCaptor = ArgumentCaptor.forClass(TapdataRecoveryEvent.class);
            verify(mockAutoRecoveryClient).enqueue(eventCaptor.capture());

            TapdataRecoveryEvent capturedEvent = eventCaptor.getValue();
            assertEquals(TapdataRecoveryEvent.RECOVERY_TYPE_DATA, capturedEvent.getRecoveryType());
            assertEquals(testRowId, capturedEvent.getRowId());
            assertNotNull(capturedEvent.getTapEvent());
            assertTrue(capturedEvent.getTapEvent() instanceof TapDeleteRecordEvent);
        }

        @Test
        void testSendCdcRecover_SourceRecordNull_TargetRecordNull_ShouldNotEnqueueEvent() throws Exception {
            setupSendCdcRecoverMocks();
            when(mockContext.getTaskId()).thenReturn("test-task-id");
            when(mockContext.getUserId()).thenReturn("test-user-id");
            when(mockContext.getUserName()).thenReturn("test-user-name");
            when(mockContext.getResultReporter()).thenReturn(mockResultReporter);

            when(mockSourceConnector.findOneByKeys(eq(testTableName), eq(testKeys), anyList()))
                .thenReturn(null);
            when(mockTargetConnector.findOneByKeys(eq("target_table"), eq(testKeys), anyList()))
                .thenReturn(null);

            taskInspectChecker.sendCdcRecover(testRowId, testTableName, testKeys);

            verify(mockResultReporter).reportOperation(
                eq(testRowId),
                eq(ResultOperationsVo.OP_AUTO_RECOVER),
                eq("test-user-id"),
                eq("test-user-name"),
                eq("")
            );

            verify(mockAutoRecoveryClient, never()).enqueue(any(TapdataRecoveryEvent.class));
        }

        @Test
        void testSendCdcRecover_TableNotFound_ShouldThrowException() {
            setupSendCdcRecoverMocks();
            when(mockContext.getTaskId()).thenReturn("test-task-id");
            when(mockContext.getUserId()).thenReturn("test-user-id");
            when(mockContext.getUserName()).thenReturn("test-user-name");
            when(mockContext.getResultReporter()).thenReturn(mockResultReporter);

            String nonExistentTable = "non_existent_table";

            TaskInspectNotFoundTableMappingException exception = assertThrows(
                TaskInspectNotFoundTableMappingException.class,
                () -> taskInspectChecker.sendCdcRecover(testRowId, nonExistentTable, testKeys)
            );

            verify(mockResultReporter).reportOperation(
                eq(testRowId),
                eq(ResultOperationsVo.OP_AUTO_RECOVER),
                eq("test-user-id"),
                eq("test-user-name"),
                eq("")
            );

            verify(mockAutoRecoveryClient, never()).enqueue(any(TapdataRecoveryEvent.class));
        }
    }

    @Nested
    class SetIgnoreTimePrecisionTest {

        @Test
        void testSetIgnoreTimePrecision_Normal_ShouldSetIgnoreTimePrecisionFalse() {
            taskInspectChecker.setIgnoreTimePrecision(TimeCheckModeEnum.NORMAL);

            verify(mockCompare).setIgnoreTimePrecision(false);
            verify(mockCompare, never()).setRoundingMode(anyString());
        }

        @Test
        void testSetIgnoreTimePrecision_Round_ShouldSetIgnoreTimePrecisionTrueWithHalfUp() {
            taskInspectChecker.setIgnoreTimePrecision(TimeCheckModeEnum.ROUND);

            verify(mockCompare).setIgnoreTimePrecision(true);
            verify(mockCompare).setRoundingMode(RoundingMode.HALF_UP.name());
        }

        @Test
        void testSetIgnoreTimePrecision_Truncate_ShouldSetIgnoreTimePrecisionTrueWithDown() {
            taskInspectChecker.setIgnoreTimePrecision(TimeCheckModeEnum.TRUNCATE);

            verify(mockCompare).setIgnoreTimePrecision(true);
            verify(mockCompare).setRoundingMode(RoundingMode.DOWN.name());
        }

        @Test
        void testSetIgnoreTimePrecision_AllModes_ShouldHandleCorrectly() {
            for (TimeCheckModeEnum mode : TimeCheckModeEnum.values()) {
                reset(mockCompare);

                taskInspectChecker.setIgnoreTimePrecision(mode);

                switch (mode) {
                    case NORMAL:
                        verify(mockCompare).setIgnoreTimePrecision(false);
                        verify(mockCompare, never()).setRoundingMode(anyString());
                        break;
                    case ROUND:
                        verify(mockCompare).setIgnoreTimePrecision(true);
                        verify(mockCompare).setRoundingMode(RoundingMode.HALF_UP.name());
                        break;
                    case TRUNCATE:
                        verify(mockCompare).setIgnoreTimePrecision(true);
                        verify(mockCompare).setRoundingMode(RoundingMode.DOWN.name());
                        break;
                    default:
                        fail("Unexpected TimeCheckModeEnum value: " + mode);
                }
            }
        }
    }
}
