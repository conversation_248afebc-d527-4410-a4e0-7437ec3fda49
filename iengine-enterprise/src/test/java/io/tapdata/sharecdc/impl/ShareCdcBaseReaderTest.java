package io.tapdata.sharecdc.impl;

import com.tapdata.entity.OperationType;
import com.tapdata.entity.sharecdc.LogContent;
import io.tapdata.exception.TapCodeException;
import io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcReaderExCode_13;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ShareCdcBaseReaderTest {
    @Nested
    class logContentVerifyTest {
        LogContent logContent;
        @BeforeEach
        void beforeEach (){
            logContent = mock(LogContent.class);
        }
        @Test
        void test_LOG_DATA_NULL() {
            logContent = null;
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.LOG_DATA_NULL;
            }
        }
        @Test
        void test_MISSING_OPERATION() {
            when(logContent.getOp()).thenReturn("");
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.MISSING_OPERATION;
            }
        }
        @Test
        void test_MISSING_TABLE_NAME() {
            when(logContent.getOp()).thenReturn("test");
            when(logContent.getFromTable()).thenReturn("");
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.MISSING_TABLE_NAME;
            }
        }
        @Test
        void test_GET_OP_TYPE_ERROR_FROM() {
            try (MockedStatic<OperationType> mb = Mockito
                    .mockStatic(OperationType.class)) {
                mb.when(()->OperationType.fromOp("test")).thenThrow(RuntimeException.class);
                when(logContent.getOp()).thenReturn("test");
                when(logContent.getFromTable()).thenReturn("test");
                try {
                    ShareCdcBaseReader.logContentVerify(logContent);
                } catch (TapCodeException e) {
                    assert e.getCode() == ShareCdcReaderExCode_13.GET_OP_TYPE_ERROR;
                }
            }
        }
        @Test
        void test_GET_OP_TYPE_ERROR() {
            when(logContent.getOp()).thenReturn("test");
            when(logContent.getFromTable()).thenReturn("test");
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.GET_OP_TYPE_ERROR;
            }
        }
        @Test
        void test_INSERT_MISSING_AFTER() {
            when(logContent.getOp()).thenReturn("i");
            when(logContent.getFromTable()).thenReturn("test");
            when(logContent.getAfter()).thenReturn(null);
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.INSERT_MISSING_AFTER;
            }
        }
        @Test
        void test_UPDATE_MISSING_AFTER() {
            when(logContent.getOp()).thenReturn("u");
            when(logContent.getFromTable()).thenReturn("test");
            when(logContent.getAfter()).thenReturn(null);
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.UPDATE_MISSING_AFTER;
            }
        }
        @Test
        void test_DELETE_MISSING_BEFORE() {
            when(logContent.getOp()).thenReturn("d");
            when(logContent.getFromTable()).thenReturn("test");
            when(logContent.getBefore()).thenReturn(null);
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.DELETE_MISSING_BEFORE;
            }
        }
        @Test
        void test_DDL_MISSING_BYTES() {
            when(logContent.getOp()).thenReturn("ddl");
            when(logContent.getFromTable()).thenReturn("test");
            when(logContent.getTapDDLEvent()).thenReturn(null);
            try {
                ShareCdcBaseReader.logContentVerify(logContent);
            } catch (TapCodeException e) {
                assert e.getCode() == ShareCdcReaderExCode_13.DDL_MISSING_BYTES;
            }
        }
    }
}
