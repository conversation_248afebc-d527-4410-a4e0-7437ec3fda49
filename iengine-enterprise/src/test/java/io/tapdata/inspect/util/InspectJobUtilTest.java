package io.tapdata.inspect.util;

import com.tapdata.entity.inspect.InspectDataSource;
import com.tapdata.entity.inspect.InspectResultStats;
import com.tapdata.mongo.ClientMongoOperator;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.exception.TapPdkBaseException;
import io.tapdata.exception.TapPdkReadMissingPrivilegesEx;
import io.tapdata.inspect.InspectTaskContext;
import io.tapdata.pdk.apis.entity.QueryOperator;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.core.error.TapPdkRunnerUnknownException;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;

import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class InspectJobUtilTest {

    @Nested
    class WrapFilterTest {
        List<QueryOperator> srcConditions;
        TapAdvanceFilter tapAdvanceFilter;
        QueryOperator operator;

        @BeforeEach
        void init() {
            operator = mock(QueryOperator.class);

            srcConditions = new ArrayList<>();
            tapAdvanceFilter = mock(TapAdvanceFilter.class);

            doNothing().when(tapAdvanceFilter).setOperators(srcConditions);
            doNothing().when(tapAdvanceFilter).setMatch(any(DataMap.class));
            doNothing().when(tapAdvanceFilter).setOperators(null);
        }

        void assertVerify(List<QueryOperator> s, int op, int getOperator, int getKey, int getValue) {
            when(operator.getOperator()).thenReturn(op);
            when(operator.getKey()).thenReturn("key");
            when(operator.getValue()).thenReturn("value");
            try(MockedStatic<TapAdvanceFilter> taf = mockStatic(TapAdvanceFilter.class);
                MockedStatic<InspectJobUtil> iju = mockStatic(InspectJobUtil.class)) {
                taf.when(TapAdvanceFilter::create).thenReturn(tapAdvanceFilter);
                iju.when(() -> InspectJobUtil.wrapFilter(s)).thenCallRealMethod();
                TapAdvanceFilter filter = InspectJobUtil.wrapFilter(s);
                Assertions.assertNotNull(filter);
                taf.verify(TapAdvanceFilter::create, times(1));
            }
            verify(tapAdvanceFilter, times(1)).setOperators(s);
            verify(tapAdvanceFilter, times(1)).setMatch(any(DataMap.class));
            verify(operator, times(getOperator)).getOperator();
            verify(operator, times(getKey)).getKey();
            verify(operator, times(getValue)).getValue();
        }

        @Test
        void testSrcConditionsIsNull() {
            assertVerify(null, 0, 0, 0, 0);
        }

        @Test
        void testSrcConditionsIsEmpty() {
            assertVerify(srcConditions, 5, 0, 0, 0);
        }

        @Test
        void testNormal() {
            srcConditions.add(operator);
            assertVerify(srcConditions, 5, 1, 1, 1);
        }
        @Test
        void testOpNotFive() {
            srcConditions.add(operator);
            assertVerify(srcConditions, 4, 1, 0, 0);
        }
    }

    @Nested
    class GetTapTableTest {
        InspectDataSource inspectDataSource;
        InspectTaskContext inspectTaskContext;
        TapTable tapTable;
        ClientMongoOperator mongoOperator;
        @BeforeEach
        void init() {
            inspectDataSource = mock(InspectDataSource.class);
            inspectTaskContext = mock(InspectTaskContext.class);
            tapTable = mock(TapTable.class);
            mongoOperator = mock(ClientMongoOperator.class);

            when(inspectDataSource.getConnectionId()).thenReturn("ConnectionId");
            when(inspectDataSource.getTable()).thenReturn("tableName");
            when(inspectTaskContext.getClientMongoOperator()).thenReturn(mongoOperator);
        }
        @Test
        void testInspectTaskContextIsNull() {
            when(mongoOperator.findOne(any(Map.class), anyString(), any(Class.class))).thenReturn(tapTable);
            try (MockedStatic<InspectJobUtil> iju = mockStatic(InspectJobUtil.class)) {
                iju.when(() -> InspectJobUtil.getTapTable(inspectDataSource, null)).thenCallRealMethod();
                TapTable tapTable = InspectJobUtil.getTapTable(inspectDataSource, null);
                Assertions.assertNotNull(tapTable);
                iju.verify(() -> InspectJobUtil.getTapTable(inspectDataSource, null), times(1));
            }
            verify(inspectDataSource, times(1)).getConnectionId();
            verify(inspectDataSource, times(1)).getTable();
            verify(inspectTaskContext, times(0)).getClientMongoOperator();
            verify(mongoOperator, times(0)).findOne(any(Map.class), anyString(), any(Class.class));
        }
        @Test
        void testFindOneTableIsNull() {
            when(mongoOperator.findOne(any(Map.class), anyString(), any(Class.class))).thenReturn(null);
            try (MockedStatic<InspectJobUtil> iju = mockStatic(InspectJobUtil.class)) {
                iju.when(() -> InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext)).thenCallRealMethod();
                TapTable tapTable = InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext);
                Assertions.assertNotNull(tapTable);
                iju.verify(() -> InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext), times(1));
            }
            verify(inspectDataSource, times(1)).getConnectionId();
            verify(inspectDataSource, times(1)).getTable();
            verify(inspectTaskContext, times(1)).getClientMongoOperator();
            verify(mongoOperator, times(1)).findOne(any(Map.class), anyString(), any(Class.class));
        }
        @Test
        void testTableAllCaseNotNull() {
            when(mongoOperator.findOne(any(Map.class), anyString(), any(Class.class))).thenReturn(tapTable);
            try (MockedStatic<InspectJobUtil> iju = mockStatic(InspectJobUtil.class)) {
                iju.when(() -> InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext)).thenCallRealMethod();
                TapTable tapTable = InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext);
                Assertions.assertNotNull(tapTable);
                iju.verify(() -> InspectJobUtil.getTapTable(inspectDataSource, inspectTaskContext), times(1));
            }
            verify(inspectDataSource, times(1)).getConnectionId();
            verify(inspectDataSource, times(1)).getTable();
            verify(inspectTaskContext, times(1)).getClientMongoOperator();
            verify(mongoOperator, times(1)).findOne(any(Map.class), anyString(), any(Class.class));
        }
        @Test
        void testTableColumnsIsNotEmpty() {
            TapTable tapTable = new TapTable();
            LinkedHashMap<String, TapField> finalFieldMap = new LinkedHashMap<>();
            TapField test1 = new TapField("test1","String");
            TapField test2 = new TapField("test2","String");
            finalFieldMap.put("test1",test1);
            finalFieldMap.put("test2",test2);
            tapTable.setNameFieldMap(finalFieldMap);
            when(mongoOperator.findOne(any(Map.class), anyString(), any(Class.class))).thenReturn(tapTable);
            InspectDataSource inputDataSource = new InspectDataSource();
            inputDataSource.setColumns(Arrays.asList("test1"));
            inputDataSource.setConnectionId("test");
            inputDataSource.setTable("test");
            TapTable table = InspectJobUtil.getTapTable(inputDataSource, inspectTaskContext);
            Assertions.assertEquals(1,table.getNameFieldMap().size());
        }

        @Test
        void testTableColumnsIsEmpty() {
            TapTable tapTable = new TapTable();
            LinkedHashMap<String, TapField> finalFieldMap = new LinkedHashMap<>();
            tapTable.setNameFieldMap(finalFieldMap);
            when(mongoOperator.findOne(any(Map.class), anyString(), any(Class.class))).thenReturn(tapTable);
            InspectDataSource inputDataSource = new InspectDataSource();
            inputDataSource.setColumns(Arrays.asList("test1"));
            inputDataSource.setConnectionId("test");
            inputDataSource.setTable("test");
            TapTable table = InspectJobUtil.getTapTable(inputDataSource, inspectTaskContext);
            Assertions.assertEquals(0,table.getNameFieldMap().size());
        }

    }


    @Nested
    class CheckRowCountInspectTaskDataSourceTest {
        InspectDataSource dataSource;
        @BeforeEach
        void init() {
            dataSource = mock(InspectDataSource.class);
        }

        void assertVerify(InspectDataSource ds, int times, int size) {
            List<String> list = InspectJobUtil.checkRowCountInspectTaskDataSource("prefix", ds);
            Assertions.assertNotNull(list);
            Assertions.assertEquals(size, list.size());
            verify(dataSource, times(times)).getConnectionId();
            verify(dataSource, times(times)).getTable();
        }

        @Test
        void testDataSourceIsNull() {
            when(dataSource.getConnectionId()).thenReturn("id");
            when(dataSource.getTable()).thenReturn("table");
            assertVerify(null, 0, 1);
        }

        @Test
        void testConnectionIdIsNull() {
            when(dataSource.getConnectionId()).thenReturn(null);
            when(dataSource.getTable()).thenReturn("table");
            assertVerify(dataSource, 1, 1);
        }

        @Test
        void testTableIsNull() {
            when(dataSource.getConnectionId()).thenReturn("id");
            when(dataSource.getTable()).thenReturn(null);
            assertVerify(dataSource, 1, 1);
        }

        @Test
        void testAllCaseIsSucceed() {
            when(dataSource.getConnectionId()).thenReturn("id");
            when(dataSource.getTable()).thenReturn("table");
            assertVerify(dataSource, 1, 0);
        }

        @Test
        void testAllCaseIsFailed() {
            when(dataSource.getConnectionId()).thenReturn(null);
            when(dataSource.getTable()).thenReturn(null);
            assertVerify(dataSource, 1, 2);
        }
    }

    @Nested
    class buildStatsErrorMsgTest {
        InspectResultStats stats;
        Throwable e;
        String msg;

        @BeforeEach
        void init() {
            stats = new InspectResultStats();
            msg = "Inspect task occur an error when inspect table";
        }

        @Test
        @DisplayName("test for TapPdkBaseException")
        void test1() {
            e = mock(TapPdkBaseException.class);
            InspectJobUtil.buildStatsErrorMsg(stats, e);
            assertTrue(stats.getErrorMsg().contains(msg));
        }
        @Test
        @DisplayName("test for TapPdkRunnerUnknownException")
        void test2() {
            e = mock(TapPdkRunnerUnknownException.class);
            InspectJobUtil.buildStatsErrorMsg(stats, e);
            assertTrue(stats.getErrorMsg().contains(msg));
        }
        @Test
        @DisplayName("test for RuntimeException")
        void test3() {
            e = mock(RuntimeException.class);
            InspectJobUtil.buildStatsErrorMsg(stats, e);
            assertFalse(stats.getErrorMsg().contains(msg));
        }
    }

    @Nested
    class buildErrorConsumerTest {
        String tableName = "test";
        Consumer<RuntimeException> consumer;
        @Test
        void testWithRuntimeException() {
            consumer = InspectJobUtil.buildErrorConsumer(tableName);
            assertThrows(RuntimeException.class, () -> consumer.accept(new RuntimeException()));
        }
        @Test
        void testWithTapPdkBaseThrowable() {
            consumer = InspectJobUtil.buildErrorConsumer(tableName);
            TapPdkBaseException tapPdkBaseException = assertThrows(TapPdkBaseException.class, () -> consumer.accept(new TapPdkReadMissingPrivilegesEx("mysql", "read", null, new RuntimeException())));
            assertEquals(tableName, tapPdkBaseException.getTableName());
        }
        @Test
        void testWithTapPdkRunnerUnknownException() {
            consumer = InspectJobUtil.buildErrorConsumer(tableName);
            TapPdkRunnerUnknownException tapPdkRunnerUnknownException = assertThrows(TapPdkRunnerUnknownException.class, () -> consumer.accept(new TapPdkRunnerUnknownException(new RuntimeException())));
            assertEquals(tableName, tapPdkRunnerUnknownException.getTableName());
        }
    }
}
