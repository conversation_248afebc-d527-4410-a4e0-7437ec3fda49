package com.tapdata.taskinspect.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * 检查项状态
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/13 18:10 Create
 */
@Setter
@Getter
public class CheckItem implements Serializable, Cloneable {

    // 校验阶段
    public static final int STEP_CHECK = 1;          // 第一次校验
    public static final int STEP_RECHECK = 2;        // 自动校验
    public static final int STEP_RECOVER = 3;        // 自动修复
    public static final int STEP_MANUAL_CHECK = 4;   // 手动校验
    public static final int STEP_MANUAL_RECOVER = 5; // 手动修复

    private int step;                           // 校验阶段
    private long ts;                            // 进队时间
    private String rowId;                       // 数据行编号
    private long cdcReadTs;                     // 增量读取时间
    private long cdcOpTs;                       // 增量变更时间
    private String tableName;                   // 表名
    private LinkedHashMap<String, Object> keys; // 主键值


    public CheckItem clone() {
        CheckItem item = new CheckItem();
        item.setStep(getStep());
        item.setTs(getTs());
        item.setRowId(getRowId());
        item.setCdcReadTs(getCdcReadTs());
        item.setCdcOpTs(getCdcOpTs());
        item.setTableName(getTableName());
        item.setKeys(getKeys());
        return item;
    }

    public static CheckItem of(String rowId, long cdcReadTs, long cdcTs, String tableName, LinkedHashMap<String, Object> keys) {
        CheckItem item = new CheckItem();
        item.setStep(STEP_CHECK);
        item.setTs(System.currentTimeMillis());
        item.setRowId(rowId);
        item.setCdcReadTs(cdcReadTs);
        item.setCdcOpTs(cdcTs);
        item.setTableName(tableName);
        item.setKeys(keys);
        return item;
    }
}
