package com.tapdata.taskinspect;

import com.tapdata.exception.CompareException;
import com.tapdata.mongo.HttpClientMongoOperator;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableFieldMappingException;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableMappingException;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务内校验-上下文
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/13 16:17 Create
 */
public class TaskInspectContext {

    /**
     * 状态上报间隔时间 ms
     */
    @Getter
    private final long reportInterval = 10 * 1000L;
    /**
     * 停止超时时间
     */
    @Getter
    private final long stopTimeout = 60 * 1000L;
    /**
     * 错误输出频率
     */
    @Getter
    private final long errorRate = 5 * 1000L;
    /**
     * 遍历等待时长
     */
    @Getter
    private final long loopMilliseconds = 500L;
    /**
     * 增量事件最大更新次数
     */
    @Getter
    private final long maxUpdateTimes = 3L;

    @Getter
    private final TaskDto task;
    @Getter
    private final String taskId;
    @Getter
    private final String userId;
    @Getter
    private final String userName;
    @Getter
    private final IOperator operator;

    @Getter
    @Setter
    private IResultReporter resultReporter;
    @Getter
    @Setter
    private TaskInspectChecker checker;
    @Getter
    @Setter
    private String manualUserId;
    @Getter
    @Setter
    private String manualUserName;
    @Getter
    @Setter
    private String manualId;
    /**
     * 是否启用增量自动校验（二次校验）
     */
    @Getter
    @Setter
    private boolean cdcRecheck = false;
    /**
     * 是否启用增量自动修复
     */
    @Getter
    @Setter
    private boolean cdcRecover = false;
    /**
     * 是否校验无主键表
     */
    @Getter
    @Setter
    private boolean checkNoPkTables = false;


    /**
     * 停止状态不可逆，设置为 true 后不可恢复
     */
    private final AtomicBoolean stopping = new AtomicBoolean(false);
    @Getter
    private boolean forceStop = false;

    @Getter
    @Setter
    private Boolean ignoreTimePrecision = false;

    @Getter
    @Setter
    private String roundingMode;

    public TaskInspectContext(TaskDto task, HttpClientMongoOperator clientMongoOperator) {
        this.task = task;
        this.operator = new TaskInspectOperator(clientMongoOperator);
        this.taskId = Optional.ofNullable(getTask().getId()).map(ObjectId::toHexString).orElse(null);
        this.userId = task.getUserId();
        this.userName = task.getCreateUser();
    }

    public boolean isStopping() {
        return stopping.get();
    }

    /**
     * 设置停止标志的方法
     *
     * @param force 是否强制停止的标志
     */
    protected void setStop(boolean force) {
        // 将forceStop标志与传入的force参数进行或运算，如果force为true，则forceStop将被设置为true
        this.forceStop = this.forceStop || force;
        // 设置stopping标志为true，表示正在请求停止
        this.stopping.set(true);
    }

    public ResultsReportVo compare(String rowId, String tableName, LinkedHashMap<String, Object> sourceKeys) throws TaskInspectNotFoundTableMappingException, TaskInspectNotFoundTableFieldMappingException, CompareException {
        return checker.compare(rowId, tableName, sourceKeys);
    }
}
