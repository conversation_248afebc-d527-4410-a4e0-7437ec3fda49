package com.tapdata.tm.webhook.impl.convert.stage;

import com.tapdata.tm.webhook.dto.WebHookAlterInfoDto;
import com.tapdata.tm.webhook.dto.WebHookInfoDto;
import com.tapdata.tm.webhook.entity.WebHookEvent;
import com.tapdata.tm.webhook.enums.ConstVariable;
import com.tapdata.tm.webhook.impl.convert.Converter;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service("webHookTaskAlter")
public class AlterInfoConverter implements Converter<Map<String, Object>> {

    @Override
    public Map<String, Object> convert(Object o, WebHookInfoDto myOpenHookInfo, WebHookEvent webHookEvent) {
        Map<String, Object> event = new HashMap<>();
        event.put("action", "TaskAlter");
        event.put(ConstVariable.HOOK_ID, myOpenHookInfo.getId().toHexString());
        event.put("actionTime", System.currentTimeMillis());
        event.put("title", webHookEvent.getTitle());
        event.put("content", webHookEvent.getContent());
        event.put("type", webHookEvent.getType());
        event.put("actionData", Optional.ofNullable(TapSimplify.fromJson(TapSimplify.toJson(o, JsonParser.ToJsonFeature.WriteMapNullValue), Map.class)).orElse(new HashMap<String, Object>()));
        return (Map<String, Object>) Optional.ofNullable(fixObject(eventData(event, myOpenHookInfo))).orElse(new HashMap<String, Object>());
    }

    @Override
    public Map<String, Object> getAllAlarmInfo(Object o) {
        try {
            if (o instanceof WebHookAlterInfoDto) {
                return Optional.ofNullable(TapSimplify.fromJson(TapSimplify.toJson(o), Map.class)).orElse(new HashMap<String, Object>());
            } else if (o instanceof Map) {
                return (Map<String, Object>) o;
            } else {
                return new HashMap<>();
            }
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> customTemplate(String customTemplate) {
        Map<String, Object> template;
        if (StringUtils.isNotBlank(customTemplate)) {
            try {
                template = Optional.ofNullable(TapSimplify.fromJsonObject(customTemplate)).orElse(new DataMap());
            } catch (Exception e) {
                log.warn("Custom template by user created not a json map, template: {}, error: {}", customTemplate, e.getMessage());
                template = new HashMap<>();
            }
        } else {
            template = new HashMap<>();
        }
        return template;
    }
}
