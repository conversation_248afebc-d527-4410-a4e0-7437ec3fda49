package com.tapdata.tm.webhook.work;

import cn.hutool.core.collection.CollUtil;
import com.tapdata.tm.alarm.constant.AlarmTypeEnum;
import com.tapdata.tm.commons.task.constant.AlarmKeyEnum;
import com.tapdata.tm.utils.Lists;
import com.tapdata.tm.utils.MongoUtils;
import com.tapdata.tm.webhook.dto.WebHookInfoDto;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import com.tapdata.tm.webhook.entity.WebHookEvent;
import com.tapdata.tm.webhook.enums.HookType;
import com.tapdata.tm.webhook.enums.PingResult;
import com.tapdata.tm.webhook.impl.WebHookHistoryServiceImpl;
import com.tapdata.tm.webhook.impl.WebHookServiceImpl;
import com.tapdata.tm.webhook.impl.convert.Converter;
import com.tapdata.tm.webhook.server.WebHookAdapterService;
import com.tapdata.tm.webhook.util.WebHookHttpUtil;
import io.tapdata.entity.simplify.TapSimplify;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@Setter(onMethod_ = {@Autowired})
@Primary
public class WebHookAdapter implements WebHookAdapterService {
    WebHookHistoryServiceImpl webHookHistoryService;
    WebHookServiceImpl webHookService;
    ApplicationContext applicationContext;
    WebHookHttpUtil webHookHttpUtil;

    @Override
    public void send(WebHookEvent event) {
        List<String> userId = event.getUserId();
        String type = event.getType();
        String metric = event.getMetric();
        if (StringUtils.isBlank(type) && StringUtils.isBlank(metric)) {
            return;
        }
        List<WebHookInfoDto> myOpenHookInfoList = webHookService.findMyOpenHookInfoList(type, metric, userId);
        sendAsync(event, myOpenHookInfoList);
    }

    @Override
    public void sendAsync(WebHookEvent event, List<WebHookInfoDto> myOpenHookInfoList) {
        if (CollUtil.isEmpty(myOpenHookInfoList)) {
            return;
        }
        CompletableFuture<Void> supplyAsync = CompletableFuture.runAsync(() -> {
        });
        myOpenHookInfoList.stream()
                .filter(Objects::nonNull)
                .forEach(webHookInfo -> supplyAsync.thenRunAsync(() -> sendAndSave(event, webHookInfo)));
    }

    @Override
    public HookOneHistory sendAndSave(WebHookEvent event, WebHookInfoDto myOpenHookInfo) {
        HookOneHistory send = null;
        String hookId = myOpenHookInfo.getId().toHexString();
        try {
            send = send(event, myOpenHookInfo);
            if (send.getHookId() == null) {
                log.warn("Webhook is no need to send again, hook id: {}, event: {}", hookId, event.getEvent());
                return send;
            }
            log.info("Webhook will be send, hook id: {}, event: {}", hookId, event.getEvent());
            webHookHistoryService.pushHistory(hookId, Lists.newArrayList(send));
            WebHookInfoDto updatePingResult = new WebHookInfoDto();
            updatePingResult.setId(MongoUtils.toObjectId(hookId));
            updatePingResult.setPingResult(PingResult.valueOf(send.getStatus()));
            webHookService.updatePingResult(updatePingResult);
            return send;
        } catch (Exception e) {
            log.error("An exception occurred during the call to WebHook, hook url: {}, hook id: {}, error message: {}",
                    myOpenHookInfo.getUrl(),
                    hookId,
                    e.getMessage(),
                    e);
        }
        return send;
    }

    @Override
    public HookOneHistory send(WebHookEvent event, WebHookInfoDto myOpenHookInfo) {
        Object eventData = event.getEvent();
        String type = event.getType();
        String metric = event.getMetric();
        HookType hookType = getHookBeanNameByType(type, metric);
        Converter<?> converter = applicationContext.getBean(hookType.getHookBeanName(), Converter.class);
        Object convert = converter.convert(eventData, myOpenHookInfo, event);
        HookOneHistory latestHookEvent = webHookHistoryService.latestHookEvent(myOpenHookInfo.getId());
        if (latestHookEvent != null && TapSimplify.toJson(convert).equals(latestHookEvent.getRequestBody()) && latestHookEvent.getRequestAt() > System.currentTimeMillis() - 3000) {
            HookOneHistory history = new HookOneHistory();
            history.setStatus(PingResult.SUCCEED.name());
            return history;
        }
        String url = myOpenHookInfo.getUrl();
        HookOneHistory history = webHookHttpUtil.getHookOneHistoryByParams(url,
                converter.analyseHead(myOpenHookInfo),
                new HashMap<>(),
                convert
        );
        history.setType(event.getMetric());
        history.setEventType(hookType.getHookName());
        history.setHookId(myOpenHookInfo.getId().toHexString());
        return webHookHttpUtil.post(history);
    }

    public static HookType getHookBeanNameByType(String type, String metric) {
        if (StringUtils.isBlank(type)) {
            return HookType.ALTER;
        }
        for (HookType value : HookType.values()) {
            if (value.getHookName().equals(type)) return value;
        }
        for (AlarmTypeEnum value : AlarmTypeEnum.values()) {
            if (value.name().equals(type)) return HookType.ALTER;
        }
        for (AlarmKeyEnum value : AlarmKeyEnum.values()) {
            if (value.name().equals(metric)) return HookType.ALTER;
        }
        return HookType.ALTER;
    }
}
