package com.tapdata.tm.roleMapping.service;

import cn.hutool.core.collection.CollUtil;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.role.dto.RoleDto;
import com.tapdata.tm.role.service.RoleService;
import com.tapdata.tm.roleMapping.dto.PrincipleType;
import com.tapdata.tm.roleMapping.dto.RoleMappingDto;
import com.tapdata.tm.roleMapping.repository.RoleMappingRepository;
import com.tapdata.tm.user.entity.User;
import com.tapdata.tm.user.repository.UserRepository;
import com.tapdata.tm.user.service.UserService;
import com.tapdata.tm.userLog.constant.Modular;
import com.tapdata.tm.userLog.constant.Operation;
import com.tapdata.tm.userLog.service.UserLogService;
import com.tapdata.tm.utils.MongoUtils;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RoleMappingServiceImpl extends RoleMappingService{
    public static final String ROLE_USERS = "roleusers";
    public static final String ID = "_id";
    private final RoleService roleService;
    private final UserService userService;
    @Autowired
    UserRepository userRepository;
    private final UserLogService userLogService;

    public RoleMappingServiceImpl(@NonNull RoleMappingRepository repository, @Lazy RoleService roleService,@Lazy UserService userService,@Lazy UserLogService userLogService) {
        super(repository);
        this.roleService = roleService;
        this.userService = userService;
        this.userLogService = userLogService;

    }

    @Override
    protected void beforeSave(RoleMappingDto dto, UserDetail userDetail) {

    }

    /**
     * 初始化用户默认角色
     *
     * @param user
     * @param userDetail
     */
    public void initUserDefaultRole(User user, UserDetail userDetail) {
        List<RoleMappingDto> roleMappings = roleService.findAll(Query.query(Criteria.where("register_user_default").is(true)))
                .stream().map(role -> {
                    RoleMappingDto roleMapping = new RoleMappingDto();
                    roleMapping.setPrincipalType("USER");
                    roleMapping.setRoleId(role.getId());
                    roleMapping.setPrincipalId(user.getId().toHexString());
                    return roleMapping;
                }).collect(Collectors.toList());

        save(roleMappings, userDetail);
    }

    public List<RoleMappingDto> getUser(PrincipleType principleType, String principleId) {
        Query query = Query.query(Criteria.where("principalId").is(principleId).and("principalType").is(principleType.getValue()));
        List<RoleMappingDto> roleMappingDtoList = findAll(query);
        if (CollectionUtils.isNotEmpty(roleMappingDtoList)) {
            for (RoleMappingDto roleMappingDto : roleMappingDtoList) {
                ObjectId roleId = roleMappingDto.getRoleId();
                RoleDto roleDto = roleService.findById(roleId);
                roleMappingDto.setRole(roleDto);
            }
        }
        return roleMappingDtoList;
    }

    public List<RoleMappingDto> getByPrincipleTypeAndPrincipleId(PrincipleType principleType, String principleId) {
        Query query = Query.query(Criteria.where("principalId").is(principleId).and("principalType").is(principleType.getValue()));
        List<RoleMappingDto> roleMappingDtoList = findAll(query);
        return roleMappingDtoList;
    }


    public List<RoleMappingDto> getByRoleIdsAndPrincipleType(PrincipleType principleType, List<ObjectId> roleIds) {
        Query query = Query.query(Criteria.where("roleId").in(roleIds).and("principalType").is(principleType.getValue()));
        List<RoleMappingDto> roleMappingDtoList = findAll(query);
        return roleMappingDtoList;
    }

    /**
     * 编辑用户，修改关联角色时调用，
     * 先删除原来principalType =USER &&principalId =userId的数据
     * 再保存前端传过来最新的
     *
     * @param roleDtos
     */
    @Transactional
    public List<RoleMappingDto> saveAll(List<RoleMappingDto> roleDtos, UserDetail userDetail) {
        for (RoleMappingDto roleMappingDto : roleDtos) {
            String uerId = roleMappingDto.getPrincipalId();
            Query query = Query.query(Criteria.where("principalType").is(PrincipleType.USER.getValue()).and("principalId").is(uerId)
                    .and("roleId").is(roleMappingDto.getRoleId()));
            deleteAll(query);
        }
        save(roleDtos, userDetail);
        addRoleForUser(roleDtos);
        if (roleDtos.size() > 0) {
            addUserLogIfNeed(roleDtos, userDetail);
        }
        return roleDtos;
    }

    public void addUserLogIfNeed(List<RoleMappingDto> dtos, UserDetail userDetail) {
        ObjectId roleId = dtos.get(0).getRoleId();
        if (null != roleId) {
            RoleDto roleDto = roleService.findById(roleId);
            if (null != roleDto) {
                userLogService.addUserLog(Modular.ROLE, Operation.UPDATE, userDetail.getUserId(), roleId.toString(), roleDto.getName());
            }
        }
    }

    @Override
    public boolean checkHasPermission(PrincipleType principleType, List<ObjectId> roleIdList, String permissionName) {
        Query query = Query.query(Criteria.where("roleId").in(roleIdList).and("principalType").is(principleType.getValue()).and("principalId").is(permissionName));
        long count = count(query);
        return count > 0;
    }

    @Override
    @Transactional
    public void removeRoleFromUser(String roleMappingId) {
        RoleMappingDto mappingDto = findOne(Query.query(Criteria.where(ID).is(MongoUtils.toObjectId(roleMappingId))));
        if (PrincipleType.USER.getValue().equalsIgnoreCase(mappingDto.getPrincipalType())) {
            ObjectId roleId = mappingDto.getRoleId();
            String userId = mappingDto.getPrincipalId();
            userService.update(Query.query(Criteria.where(ID).is(MongoUtils.toObjectId(userId))), new Update().pull(ROLE_USERS, roleId.toHexString()));
        }
        deleteById(MongoUtils.toObjectId(roleMappingId));
    }

    public void addRoleForUser(List<RoleMappingDto> roleDto) {
        if (CollUtil.isEmpty(roleDto)) return;
        BulkOperations bos = userRepository.bulkOperations(BulkOperations.BulkMode.ORDERED);
        roleDto.stream()
                .filter(Objects::nonNull)
                .filter(role -> PrincipleType.USER.getValue().equalsIgnoreCase(role.getPrincipalType()))
                .forEach(role ->
                    bos.updateOne(
                            Query.query(Criteria.where(ID).is(MongoUtils.toObjectId(role.getPrincipalId()))),
                            new Update().addToSet(ROLE_USERS, role.getRoleId().toHexString())
                    )
        );
        bos.execute();
    }

}
