package com.tapdata.tm.role.service;

import com.tapdata.tm.Permission.entity.PermissionEntity;
import com.tapdata.tm.Permission.service.PermissionService;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.base.dto.BaseDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.role.dto.RoleDto;
import com.tapdata.tm.role.repository.RoleRepository;
import com.tapdata.tm.roleMapping.dto.PrincipleType;
import com.tapdata.tm.roleMapping.dto.RoleMappingDto;
import com.tapdata.tm.roleMapping.service.RoleMappingService;
import com.tapdata.tm.userLog.constant.Modular;
import com.tapdata.tm.userLog.constant.Operation;
import com.tapdata.tm.userLog.service.UserLogService;
import com.tapdata.tm.utils.Lists;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl extends RoleService{

    @Autowired
    private RoleMappingService roleMappingService;

    @Autowired
    private PermissionService permissionService;

    private final UserLogService userLogService;

    public RoleServiceImpl(@NonNull RoleRepository repository,@Lazy UserLogService userLogService) {
        super(repository);
        this.userLogService = userLogService;
    }

    @Override
    protected void beforeSave(RoleDto dto, UserDetail userDetail) {

    }

    @Override
    public Page<RoleDto> find(Filter filter, UserDetail userDetail) {
        if (userDetail.isRoot()) {
            return super.find(filter, userDetail);
        }

        Where where = filter.getWhere();
        if (null != where) {
            where.clear();
        } else {
            filter.setWhere(new Where());
        }
        filter.getWhere().and("$or", Lists.newArrayList(new Where().and("user_id", userDetail.getUserId()), new Where().and("name", "DefaultRoleForNewUser")));
        return find(filter);
    }

    @Override
    public boolean deleteById(ObjectId objectId, UserDetail userDetail) {

        long count = roleMappingService.count(Query.query(Criteria.where("principalType").is("USER").and("roleId").is(objectId)));
        if (count > 0) {
            throw new BizException("Role.Unable.Delete");
        }

        RoleDto roleDto = findById(objectId);
        boolean deleted = super.deleteById(objectId, userDetail);
        if (deleted && null != roleDto) {
            userLogService.addUserLog(Modular.ROLE, Operation.DELETE, userDetail.getUserId(), roleDto.getId().toString(), roleDto.getName());
        }
        return deleted;
    }

    @Override
    public long updateByWhere(Where where, Document doc, UserDetail userDetail) {
        List<RoleDto> roles = findAll(where);
        long updated = super.updateByWhere(where, doc, userDetail);
        if (updated > 0 && CollectionUtils.isNotEmpty(roles) && null != roles.get(0)) {
            userLogService.addUserLog(Modular.ROLE, Operation.UPDATE, userDetail.getUserId(), roles.get(0).getId().toString(), roles.get(0).getName());
        }
        return updated;
    }

    @Override
    public <T extends BaseDto> RoleDto save(RoleDto dto, UserDetail userDetail) {
        boolean isCreate = false;
        if (dto.getId() == null) {
            isCreate = true;
        }
        RoleDto roleDto = super.save(dto, userDetail);
        //mapping default permission
        if (isCreate) {
            Filter filter = new Filter(Where.where("version", new HashMap<String, String>() {{
                put("$ne", "v2");
            }}));
            List<PermissionEntity> permissionEntities = permissionService.find(filter);
            List<RoleMappingDto> roleMappingDtos = permissionEntities.stream().map(p -> {
                RoleMappingDto roleMappingDto = new RoleMappingDto();
                roleMappingDto.setRoleId(roleDto.getId());
                roleMappingDto.setPrincipalId(p.getName());
                roleMappingDto.setPrincipalType(PrincipleType.PERMISSION.getValue());
                return roleMappingDto;
            }).collect(Collectors.toList());
            roleMappingService.save(roleMappingDtos, userDetail);
            userLogService.addUserLog(Modular.ROLE, Operation.CREATE, userDetail.getUserId(), roleDto.getId().toString(), roleDto.getName());
        } else {
            userLogService.addUserLog(Modular.ROLE, Operation.UPDATE, userDetail.getUserId(), roleDto.getId().toString(), roleDto.getName());
        }

        return roleDto;
    }
}
