package com.tapdata.tm.taskinspect.entity;

import com.tapdata.tm.base.entity.BaseEntity;
import com.tapdata.tm.taskinspect.cons.DiffTypeEnum;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 任务内校验-校验差异结果
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/1/17 21:52 Create
 */
@Data
@Document("TaskInspectResults")
@EqualsAndHashCode(callSuper = true)
public class TaskInspectResultsEntity extends BaseEntity {
    private String taskId;
    private Set<String> histories;
    private String rowId;
    private Map<String, Object> keys;
    private String sourceTable;
    private List<String> sourceFields;
    private Map<String, Object> source;
    private String targetTable;
    private List<String> targetFields;
    private Map<String, Object> target;
    private DiffTypeEnum diffType;
    private List<String> diffFields;
    private List<ResultOperationsVo> operations;
    private String manualId;
}
