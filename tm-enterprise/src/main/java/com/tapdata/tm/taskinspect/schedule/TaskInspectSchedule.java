package com.tapdata.tm.taskinspect.schedule;

import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.service.TaskInspectHistoriesService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Setter(onMethod_ = {@Autowired})
public class TaskInspectSchedule {
    private TaskInspectHistoriesService historiesService;

    /**
     * 清理心跳超时的校验 Job 状态
     */
    @Scheduled(fixedDelay = 10 * 1000)
    public void schedule() {
        long matchCounts = historiesService.updateErrorWithPingTimeout();
        if (0 < matchCounts) {
            log.info("{} clean {} ping timeout jobs", TaskInspectUtils.MODULE_NAME, matchCounts);
        }
    }
}
