package com.tapdata.tm.taskinspect.aop;

import com.tapdata.tm.taskinspect.service.TaskInspectHistoriesService;
import com.tapdata.tm.taskinspect.service.TaskInspectResultsOpService;
import com.tapdata.tm.taskinspect.service.TaskInspectResultsService;
import com.tapdata.tm.taskinspect.service.TaskInspectService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/24 17:06 Create
 */
@Aspect
@Component
@Slf4j
public class TaskInspectAop {

    private final TaskInspectService service;
    private final TaskInspectHistoriesService historiesService;
    private final TaskInspectResultsService resultsService;
    private final TaskInspectResultsOpService resultsOpService;

    @Autowired
    public TaskInspectAop(TaskInspectService service, TaskInspectHistoriesService historiesService, TaskInspectResultsService resultsService, TaskInspectResultsOpService resultsOpService) {
        this.service = service;
        this.historiesService = historiesService;
        this.resultsService = resultsService;
        this.resultsOpService = resultsOpService;
    }

    @Pointcut("execution(* com.tapdata.tm.task.service.TaskResetLogService.clearLogByTaskId(String))")
    public void renewPointCut() {
    }

    @After("renewPointCut()")
    public Object afterUpdateByIdPointcut(JoinPoint joinPoint) {
        Object taskId = joinPoint.getArgs()[0];
        if (null != taskId) {
            Query query = Query.query(Criteria.where("taskId").is(taskId));
            resultsOpService.deleteAll(query);
            resultsService.deleteAll(query);
            historiesService.deleteAll(query);
        }
        return joinPoint.getTarget();
    }

}
