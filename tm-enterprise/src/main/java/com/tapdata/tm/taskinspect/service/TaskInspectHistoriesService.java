package com.tapdata.tm.taskinspect.service;

import com.mongodb.client.result.UpdateResult;
import com.tapdata.tm.base.service.BaseService;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.taskinspect.cons.JobStatusEnum;
import com.tapdata.tm.taskinspect.dto.TaskInspectHistoriesDto;
import com.tapdata.tm.taskinspect.entity.TaskInspectHistoriesEntity;
import com.tapdata.tm.taskinspect.repository.TaskInspectHistoriesRepository;
import com.tapdata.tm.taskinspect.vo.JobReportVo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/1/17 21:55 Create
 */
@Slf4j
@Service
public class TaskInspectHistoriesService extends BaseService<TaskInspectHistoriesDto, TaskInspectHistoriesEntity, ObjectId, TaskInspectHistoriesRepository> {

    public TaskInspectHistoriesService(@NonNull TaskInspectHistoriesRepository repository) {
        super(repository, TaskInspectHistoriesDto.class, TaskInspectHistoriesEntity.class);
    }

    @Override
    protected void beforeSave(TaskInspectHistoriesDto dto, UserDetail userDetail) {
    }

    public long updateErrorWithPingTimeout() {
        Query query = Query.query(Criteria.where(TaskInspectHistoriesDto.FIELD_STATUS).is(JobStatusEnum.RUNNING)
            .and(TaskInspectHistoriesDto.FIELD_PING_TIME).lt(System.currentTimeMillis() - 30 * 1000));
        Update update = Update.update(TaskInspectHistoriesDto.FIELD_STATUS, JobStatusEnum.PING_TIMEOUT)
            .set(TaskInspectHistoriesDto.FIELD_MESSAGE, "Ping is timeout");
        UpdateResult result = super.updateMany(query, update);
        return result.getMatchedCount();
    }

    public boolean statusReport(ObjectId id, JobReportVo vo) {
        Object status = vo.getStatus();
        Object timestamp = vo.getTimestamp();
        if (null == status || null == timestamp) return false;

        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = Update.update(TaskInspectHistoriesDto.FIELD_STATUS, status)
            .set(TaskInspectHistoriesDto.FIELD_PING_TIME, timestamp);

        for (String key : vo.keySet()) {
            switch (key) {
                case JobReportVo.FIELD_CONFIG:
                    update.set(TaskInspectHistoriesDto.FIELD_CONFIG, vo.getConfig());
                    break;
                case JobReportVo.FIELD_MESSAGE:
                    update.set(TaskInspectHistoriesDto.FIELD_MESSAGE, vo.getMessage());
                    break;
                case JobReportVo.FIELD_ATTRS:
                    update.set(TaskInspectHistoriesDto.FIELD_ATTRS, vo.getAttrs());
                    break;
                default:
                    break;
            }
        }
        if (JobStatusEnum.STOPPED.name().equals(vo.getStatus())) {
            update.set(TaskInspectHistoriesDto.FIELD_END_TIME, timestamp);
        } else if (JobStatusEnum.STARTING.name().equals(vo.getStatus())) {
            update.set(TaskInspectHistoriesDto.FIELD_BEGIN_TIME, timestamp);
            update.set(TaskInspectHistoriesDto.FIELD_END_TIME, null);
        }
        UpdateResult result = super.update(query, update);
        return result.getModifiedCount() > 0;
    }
}
