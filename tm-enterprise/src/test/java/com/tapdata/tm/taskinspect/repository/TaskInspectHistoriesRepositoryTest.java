package com.tapdata.tm.taskinspect.repository;

import com.tapdata.tm.base.reporitory.BaseRepository;
import io.tapdata.utils.UnitTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/4/16 17:33 Create
 */
@ExtendWith(MockitoExtension.class)
class TaskInspectHistoriesRepositoryTest {

    @Mock
    TaskInspectHistoriesRepository taskInspectHistoriesRepository;
    @Mock
    MongoTemplate mongoOperations;

    @BeforeEach
    void setUp() {
        taskInspectHistoriesRepository = mock(TaskInspectHistoriesRepository.class, CALLS_REAL_METHODS);
        UnitTestUtils.injectField(BaseRepository.class, taskInspectHistoriesRepository, "mongoOperations", mongoOperations);
        reset(taskInspectHistoriesRepository, mongoOperations);
    }

    @Nested
    class initTest {

        @Test
        void testInit_CollectionExists() {
            // 预定义
            String collectionName = "taskInspectHistories";
            doReturn(collectionName).when(taskInspectHistoriesRepository).getCollectionName();
            doReturn(true).when(mongoOperations).collectionExists(eq(collectionName));

            // 行为
            taskInspectHistoriesRepository.init();

            // 预期检查
            verify(mongoOperations).collectionExists(collectionName);
            verify(mongoOperations, never()).indexOps(collectionName);
        }

        @Test
        void testInit_CollectionNotExists() {
            // 预定义
            String collectionName = "taskInspectHistories";
            doReturn(collectionName).when(taskInspectHistoriesRepository).getCollectionName();
            doReturn(false).when(mongoOperations).collectionExists(eq(collectionName));
            IndexOperations indexOperations = mock(IndexOperations.class);
            doReturn(indexOperations).when(mongoOperations).indexOps(anyString());
            doReturn(null).when(indexOperations).ensureIndex(any());

            // 行为
            taskInspectHistoriesRepository.init();

            // 预期检查
            verify(mongoOperations).collectionExists(collectionName);
            verify(mongoOperations, times(3)).indexOps(collectionName);
            verify(indexOperations, times(3)).ensureIndex(any(Index.class));
        }
    }
}
