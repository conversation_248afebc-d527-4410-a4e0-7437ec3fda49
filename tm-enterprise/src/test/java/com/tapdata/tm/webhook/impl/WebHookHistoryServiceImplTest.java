package com.tapdata.tm.webhook.impl;


import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.tm.Unit4Util;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.utils.MessageUtil;
import com.tapdata.tm.webhook.dto.HookOneHistoryDto;
import com.tapdata.tm.webhook.dto.WebHookHistoryDto;
import com.tapdata.tm.webhook.dto.WebHookInfoDto;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import com.tapdata.tm.webhook.entity.WebHookHistory;
import com.tapdata.tm.webhook.enums.PingResult;
import com.tapdata.tm.webhook.params.HistoryPageParam;
import com.tapdata.tm.webhook.repository.WebHookHistoryRepository;
import com.tapdata.tm.webhook.server.WebHookService;
import com.tapdata.tm.webhook.util.WebHookHttpUtil;
import com.tapdata.tm.webhook.vo.WebHookHistoryInfoVo;
import com.tapdata.tm.webhook.vo.WebHookInfoVo;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static com.mongodb.assertions.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WebHookHistoryServiceImplTest {
    WebHookHistoryServiceImpl service;
    WebHookHistoryRepository webHookHistoryRepository;
    WebHookHttpUtil webHookHttpUtil;
    WebHookService<WebHookInfoVo> webHookService;
    WebHookHistoryRepository repository;

    UserDetail user;
    Logger log;
    @BeforeEach
    void init() {
        user = mock(UserDetail.class);
        log = mock(Logger.class);
        service = mock(WebHookHistoryServiceImpl.class);
        repository = mock(WebHookHistoryRepository.class);
        webHookHistoryRepository = mock(WebHookHistoryRepository.class);
        webHookHttpUtil = mock(WebHookHttpUtil.class);
        webHookService = mock(WebHookService.class);
        doCallRealMethod().when(service).setMaxHistoryLength(200);
        doCallRealMethod().when(service).setWebHookHistoryRepository(webHookHistoryRepository);
        doCallRealMethod().when(service).setWebHookHttpUtil(webHookHttpUtil);
        doCallRealMethod().when(service).setWebHookService(webHookService);
        ReflectionTestUtils.setField(service, "repository", repository);

        service.setMaxHistoryLength(200);
        service.setWebHookHistoryRepository(webHookHistoryRepository);
        service.setWebHookHttpUtil(webHookHttpUtil);
        service.setWebHookService(webHookService);

        Unit4Util.mockSlf4jLog(service, log);
    }

    @Nested
    class ListTest {
        HistoryPageParam pageParam;
        Locale locale;
        Aggregation aggregation;
        AggregationResults<WebHookHistoryDto> aggregate;
        List<WebHookHistoryDto> mappedResults;

        WebHookHistoryDto dto;
        WebHookHistoryDto dto1;
        List<HookOneHistory> hookEvents;
        HookOneHistory history;

        @BeforeEach
        void init() {
            ObjectId id = new ObjectId();
            pageParam = new HistoryPageParam();
            pageParam.setPageSize(10);
            pageParam.setPageFrom(10);
            pageParam.setHookId(id.toHexString());

            locale = mock(Locale.class);

            aggregation = mock(Aggregation.class);
            aggregate = mock(AggregationResults.class);
            mappedResults = new ArrayList<>();
            hookEvents = new ArrayList<>();
            history = new HookOneHistory();

            dto1 = new WebHookHistoryDto();
            dto1.setHookId(new ObjectId().toHexString());
            dto1.setEventCount(1L);
            dto1.setHookEvent(hookEvents);

            dto = new WebHookHistoryDto();
            dto.setHookId(id.toHexString());
            dto.setEventCount(1L);
            dto.setHookEvent(hookEvents);

            mappedResults.add(dto1);
            mappedResults.add(dto);
            mappedResults.add(null);

            hookEvents.add(history);
            history.setId(new ObjectId());
            history.setCreateAt(new Date());
            doNothing().when(service).copy(any(), any());

            when(webHookHistoryRepository.aggregate(aggregation, WebHookHistoryDto.class)).thenReturn(aggregate);
            when(aggregate.getMappedResults()).thenReturn(mappedResults);

            when(service.list(pageParam, user, locale)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class)) {
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Page<WebHookHistoryInfoVo> page = service.list(pageParam, user, locale);
                Assertions.assertNotNull(page);
                assertEquals(1L, page.getTotal());
                Assertions.assertNotNull(page.getItems());
                assertEquals(1, page.getItems().size());

                verify(webHookHistoryRepository).aggregate(aggregation, WebHookHistoryDto.class);
                verify(aggregate).getMappedResults();
                verify(service).copy(any(), any());
            }
        }

        @Test
        void testPageParamIsNull() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class)) {
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                when(service.list(null, user, locale)).thenCallRealMethod();
                Page<WebHookHistoryInfoVo> page = service.list(null, user, locale);
                Assertions.assertNotNull(page);
                assertEquals(0L, page.getTotal());
                Assertions.assertNotNull(page.getItems());
                assertEquals(0, page.getItems().size());

                verify(webHookHistoryRepository, times(0)).aggregate(aggregation, WebHookHistoryDto.class);
                verify(aggregate, times(0)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
            }
        }

        @Test
        void testMappedResultsIsEmpty() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class)) {
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                mappedResults.clear();
                Page<WebHookHistoryInfoVo> page = service.list(pageParam, user, locale);
                Assertions.assertNotNull(page);
                assertEquals(0L, page.getTotal());
                Assertions.assertNotNull(page.getItems());
                assertEquals(0, page.getItems().size());
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistoryDto.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
            }
        }

        @Test
        void testCollectEmpty() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class)) {
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                dto.setHookId(new ObjectId().toHexString());
                Page<WebHookHistoryInfoVo> page = service.list(pageParam, user, locale);
                Assertions.assertNotNull(page);
                assertEquals(0L, page.getTotal());
                Assertions.assertNotNull(page.getItems());
                assertEquals(0, page.getItems().size());
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistoryDto.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
            }
        }

        @Test
        void testHookEventsIsEmpty() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class)) {
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                hookEvents.clear();
                Page<WebHookHistoryInfoVo> page = service.list(pageParam, user, locale);
                Assertions.assertNotNull(page);
                assertEquals(0L, page.getTotal());
                Assertions.assertNotNull(page.getItems());
                assertEquals(0, page.getItems().size());
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistoryDto.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
            }
        }

    }

    @Nested
    class BeforeSaveTest {
        @Test
        void testNormal() {
            doCallRealMethod().when(service).beforeSave(null, null);
            Assertions.assertDoesNotThrow(() -> service.beforeSave(null, null));
        }
    }

    @Nested
    class ReSend {
        HookOneHistoryDto history;
        String hookId;
        ObjectId historyId;
        WebHookInfoVo webHookInfo;

        Aggregation aggregation;
        AggregationResults<WebHookHistory> aggregate;
        List<WebHookHistory> results;
        WebHookHistory hookHistory;
        List<HookOneHistory> hookEvent;
        HookOneHistory one;
        HookOneHistory one1;
        HookOneHistory one2;
        @BeforeEach
        void init() {
            historyId = new ObjectId();
            hookId = new ObjectId().toHexString();
            history = new HookOneHistoryDto();
            history.setHookId(hookId);
            history.setId(historyId);
            history.setUrl("url");
            history.setCreateAt(new Date());
            history.setStatus(PingResult.SUCCEED.name());

            webHookInfo = new WebHookInfoVo();
            webHookInfo.setCreateAt(new Date());
            when(webHookService.findWebHookByHookId(hookId, user)).thenReturn(webHookInfo);

            doNothing().when(webHookService).checkUrl("url");
            aggregation = mock(Aggregation.class);
            aggregate = mock(AggregationResults.class);
            results = new ArrayList<>();
            hookEvent = new ArrayList<>();

            hookHistory = new WebHookHistory();
            hookHistory.setCreateAt(new Date());
            hookHistory.setHookEvent(hookEvent);
            results.add(null);
            results.add(hookHistory);

            one = new HookOneHistory();
            one.setId(new ObjectId());
            one.setCreateAt(new Date());
            one1 = new HookOneHistory();
            one1.setId(historyId);
            one1.setCreateAt(new Date());
            one1.setStatus(PingResult.SUCCEED.name());
            one2 = new HookOneHistory();
            one2.setCreateAt(new Date());
            one2.setId(null);

            hookEvent.add(null);
            hookEvent.add(one);
            hookEvent.add(one1);
            hookEvent.add(one2);

            when(webHookHistoryRepository.aggregate(aggregation, WebHookHistory.class)).thenReturn(aggregate);
            when(aggregate.getMappedResults()).thenReturn(results);

            when(webHookHttpUtil.post(any(HookOneHistory.class))).thenReturn(one1);
            when(webHookHttpUtil.post(null)).thenReturn(one1);

            when(service.pushHistory(anyString(), anyList())).thenReturn(1L);
            doNothing().when(service).updateHistory(hookId, one1, user);
            when(webHookService.updatePingResult(any(WebHookInfoDto.class))).thenReturn(webHookInfo);

            doNothing().when(service).copy(any(), any());
            when(service.reSend(history, user)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertDoesNotThrow(() -> service.reSend(history, user));
                verify(webHookService, times(1)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(1)).checkUrl("url");
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(1)).copy(any(), any());
                verify(webHookHttpUtil).post(any(HookOneHistory.class));
                verify(service, times(0)).pushHistory(anyString(), anyList());
                verify(service, times(1)).updateHistory(hookId, one1, user);
                verify(webHookService, times(1)).updatePingResult(any(WebHookInfoDto.class));
            }
        }
        @Test
        void testHistoryIdIsEmpty() {
            history.setId(null);
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertThrows(BizException.class, () -> {
                    try{
                        service.reSend(history, user);
                    } catch (BizException e) {
                        assertEquals("webhook.history.reSend", e.getErrorCode());
                        throw e;
                    }
                });
                verify(webHookService, times(0)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(0)).checkUrl("url");
                verify(webHookHistoryRepository, times(0)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(0)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
                verify(webHookHttpUtil, times(0)).post(any(HookOneHistory.class));
                verify(service, times(0)).pushHistory(anyString(), anyList());
                verify(service, times(0)).updateHistory(hookId, one1, user);
                verify(webHookService, times(0)).updatePingResult(any(WebHookInfoDto.class));
            }
        }
        @Test
        void testWebHookInfoEmpty() {
            when(webHookService.findWebHookByHookId(hookId, user)).thenReturn(null);
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertThrows(BizException.class, () -> {
                    try{
                        service.reSend(history, user);
                    } catch (BizException e) {
                        assertEquals("webhook.info.existsById", e.getErrorCode());
                        throw e;
                    }
                });
                verify(webHookService, times(1)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(0)).checkUrl("url");
                verify(webHookHistoryRepository, times(0)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(0)).getMappedResults();
                verify(service, times(0)).copy(any(), any());
                verify(webHookHttpUtil, times(0)).post(any(HookOneHistory.class));
                verify(service, times(0)).pushHistory(anyString(), anyList());
                verify(service, times(0)).updateHistory(hookId, one1, user);
                verify(webHookService, times(0)).updatePingResult(any(WebHookInfoDto.class));
            }
        }


        @Test
        void testResultsIsEmpty() {
            results.clear();
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertDoesNotThrow(() -> service.reSend(history, user));
                verify(webHookService, times(1)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(1)).checkUrl("url");
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(2)).copy(any(), any());
                verify(webHookHttpUtil).post(any(HookOneHistory.class));
                verify(service, times(1)).pushHistory(anyString(), anyList());
                verify(service, times(0)).updateHistory(hookId, one1, user);
                verify(webHookService, times(1)).updatePingResult(any(WebHookInfoDto.class));
            }
        }


        @Test
        void testHookEventIsEmpty() {
            hookEvent.clear();
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertDoesNotThrow(() -> service.reSend(history, user));
                verify(webHookService, times(1)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(1)).checkUrl("url");
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(2)).copy(any(), any());
                verify(webHookHttpUtil).post(any(HookOneHistory.class));
                verify(service, times(1)).pushHistory(anyString(), anyList());
                verify(service, times(0)).updateHistory(hookId, one1, user);
                verify(webHookService, times(1)).updatePingResult(any(WebHookInfoDto.class));
            }
        }

        @Test
        void testAfterFilterHookEventIsEmpty() {
            one1.setId(new ObjectId());
            try(MockedStatic<Aggregation> a = mockStatic(Aggregation.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("message");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("message");
                a.when(() -> Aggregation.newAggregation(anyList())).thenReturn(aggregation);
                Assertions.assertDoesNotThrow(() -> service.reSend(history, user));
                verify(webHookService, times(1)).findWebHookByHookId(hookId, user);
                verify(webHookService, times(1)).checkUrl("url");
                verify(webHookHistoryRepository, times(1)).aggregate(aggregation, WebHookHistory.class);
                verify(aggregate, times(1)).getMappedResults();
                verify(service, times(2)).copy(any(), any());
                verify(webHookHttpUtil).post(any(HookOneHistory.class));
                verify(service, times(1)).pushHistory(anyString(), anyList());
                verify(service, times(0)).updateHistory(hookId, one1, user);
                verify(webHookService, times(1)).updatePingResult(any(WebHookInfoDto.class));
            }
        }
    }

    @Nested
    class UpdateHistoryTest {
        HookOneHistory history;
        @Test
        void init() {
            String id = new ObjectId().toHexString();
            history = new HookOneHistory();
            history.setId(new ObjectId());
            when(service.update(any(Query.class), any(Update.class), any(UserDetail.class))).thenReturn(mock(UpdateResult.class));
            doCallRealMethod().when(service).updateHistory(id, history, user);
            service.updateHistory(id, history, user);
            verify(service).update(any(Query.class), any(Update.class), any(UserDetail.class));
        }
    }

    @Nested
    class PushHistoryTest {
        List<HookOneHistory> history;
        @Test
        void testNormal() {
            String hookId = new ObjectId().toHexString();
            history = new ArrayList<>();
            HookOneHistory one = new HookOneHistory();
            one.setId(new ObjectId());
            HookOneHistory one1 = new HookOneHistory();

            history.add(one);
            history.add(one1);
            history.add(null);
            when(service.buildOneHistory(anyString(), anyList())).thenCallRealMethod();
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(1L);
            when(repository.upsert(any(Query.class), any(Update.class))).thenReturn(result);
            when(service.pushHistory(hookId, history)).thenCallRealMethod();
            service.pushHistory(hookId, history);
            verify(repository).upsert(any(Query.class), any(Update.class));
        }

        @Test
        void testHistoryIsEmpty() {
            String hookId = new ObjectId().toHexString();
            history = new ArrayList<>();
            when(service.buildOneHistory(anyString(), anyList())).thenCallRealMethod();
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(1L);
            when(repository.upsert(any(Query.class), any(Update.class))).thenReturn(result);
            when(service.pushHistory(hookId, history)).thenCallRealMethod();
            service.pushHistory(hookId, history);
            verify(repository, times(0)).upsert(any(Query.class), any(Update.class));
        }
    }

    @Nested
    class PushManyHistoryTest {
        List<Pair<String, List<HookOneHistory>>> historyInfos;
        BulkOperations bulkOperations;
        BulkWriteResult execute;
        @BeforeEach
        void init() {
            historyInfos = new ArrayList<>();
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(1L);

            bulkOperations = mock(BulkOperations.class);

            execute = mock(BulkWriteResult.class);
            when(execute.getMatchedCount()).thenReturn(1);

            when(service.buildOneHistory(anyString(), anyList())).thenCallRealMethod();
            when(service.pushManyHistory(historyInfos)).thenCallRealMethod();
            when(repository.bulkOperations(BulkOperations.BulkMode.ORDERED)).thenReturn(bulkOperations);
            when(bulkOperations.upsert(anyList())).thenReturn(bulkOperations);
            when(bulkOperations.execute()).thenReturn(execute);

            historyInfos.add(mockOne(false, false));
            historyInfos.add(mockOne(false, true));
            historyInfos.add(mockOne(true, true));
        }

        Pair<String, List<HookOneHistory>> mockOne(boolean isEmptyHookId, boolean isEmptyList) {
            String hookId = new ObjectId().toHexString();
            List<HookOneHistory> history = new ArrayList<>();
            HookOneHistory one = new HookOneHistory();
            one.setId(new ObjectId());
            HookOneHistory one1 = new HookOneHistory();
            history.add(one);
            history.add(one1);
            history.add(null);
            return Pair.of(isEmptyHookId ? "" : hookId, isEmptyList ? new ArrayList<>() : history);
        }

        @Test
        void testNormal() {
            service.pushManyHistory(historyInfos);
            verify(execute, times(1)).getMatchedCount();
            verify(service, times(1)).buildOneHistory(anyString(), anyList());
            verify(repository, times(1)).bulkOperations(BulkOperations.BulkMode.ORDERED);
            verify(bulkOperations, times(1)).upsert(anyList());
            verify(bulkOperations, times(1)).execute();
        }

        @Test
        void testHistoryInfosIsEmpty() {
            historyInfos.clear();
            assertEquals(0L, service.pushManyHistory(historyInfos));
            verify(execute, times(0)).getMatchedCount();
            verify(service, times(0)).buildOneHistory(anyString(), anyList());
            verify(repository, times(0)).bulkOperations(BulkOperations.BulkMode.ORDERED);
            verify(bulkOperations, times(0)).upsert(anyList());
            verify(bulkOperations, times(0)).execute();
        }

        @Test
        void testQueryParamsIsEmpty() {
            historyInfos.clear();
            historyInfos.add(mockOne(true, true));
            assertEquals(0L, service.pushManyHistory(historyInfos));
            verify(execute, times(0)).getMatchedCount();
            verify(service, times(0)).buildOneHistory(anyString(), anyList());
            verify(repository, times(0)).bulkOperations(BulkOperations.BulkMode.ORDERED);
            verify(bulkOperations, times(0)).upsert(anyList());
            verify(bulkOperations, times(0)).execute();
        }
    }

    @Nested
    class DeleteHookHistoryTest {
        @Test
        void testNormal() {
            String hookId = new ObjectId().toHexString();
            doNothing().when(service).deleteAll(any(Query.class), any(UserDetail.class));
            doCallRealMethod().when(service).deleteHookHistory(hookId, user);
            service.deleteHookHistory(hookId, user);
            verify(service).deleteAll(any(Query.class), any(UserDetail.class));
        }
    }

    @Nested
    class latestHookEventTest {
        @Test
        void testNormal() {
            ObjectId hookId = new ObjectId();
            WebHookHistory webHookHistory = new WebHookHistory();
            HookOneHistory oneHistory = new HookOneHistory();
            webHookHistory.setHookEvent(Arrays.asList(oneHistory));
            Optional<WebHookHistory> latestHookEvent = Optional.of(webHookHistory);
            when(repository.findOne(any(Query.class))).thenReturn(latestHookEvent);
            doCallRealMethod().when(service).latestHookEvent(hookId);
            HookOneHistory history = service.latestHookEvent(hookId);
            assertEquals(oneHistory, history);
        }

        @Test
        void testWhenLatestHookEventIsNull() {
            ObjectId hookId = new ObjectId();
            when(repository.findOne(any(Query.class))).thenReturn(null);
            HookOneHistory history = service.latestHookEvent(hookId);
            assertNull(history);
        }
    }
}