package com.tapdata.tm.webhook.util;


import com.tapdata.tm.Unit4Util;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import io.tapdata.entity.simplify.TapSimplify;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.slf4j.Logger;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WebHookHttpUtilTest {
    WebHookHttpUtil webHookHttpUtil;
    Logger log;
    @BeforeEach
    void init() {
        log = mock(Logger.class);
        webHookHttpUtil = new WebHookHttpUtil();
        webHookHttpUtil.setConnectionRequestTimeout(3000);
        webHookHttpUtil.setConnectTimout(3000);
        webHookHttpUtil.setHttpRetryTimes(3);
        webHookHttpUtil.getConnectionRequestTimeout();
        webHookHttpUtil.getConnectTimout();
        webHookHttpUtil.getHttpRetryTimes();
        Unit4Util.mockSlf4jLog(webHookHttpUtil, log);
    }

    @Nested
    class CheckURLTest {
        @BeforeEach
        void init() {
            webHookHttpUtil = mock(WebHookHttpUtil.class);
            when(webHookHttpUtil.checkURL(anyString())).thenCallRealMethod();
            when(webHookHttpUtil.checkURL(null)).thenCallRealMethod();
            doNothing().when(log).warn(anyString());
        }
        @Test
        void testNormal() {
            Assertions.assertTrue(webHookHttpUtil.checkURL("http://127.0.0.1:666"));
            verify(log, times(0)).warn(anyString());
        }
        @Test
        void testNullUrl() {
            Assertions.assertFalse(webHookHttpUtil.checkURL(null));
            verify(log, times(1)).warn(anyString());
        }
        @Test
        void testInvalidUrl() {
            Assertions.assertFalse(webHookHttpUtil.checkURL("hello, this is invalid url"));
            verify(log, times(1)).warn(anyString());
        }
    }

    @Nested
    class PostTest {
        String url;
        Map<String, Object> head;
        Map<String, Object> urlParam;
        Object body;
        HookOneHistory history;
        Header[] headers;
        CloseableHttpClient httpClient;
        CloseableHttpResponse response;
        StatusLine statusLine;
        HttpEntity httpEntity;
        Header[] httpHeads;
        @BeforeEach
        void init() throws IOException {
            statusLine = mock(StatusLine.class);
            httpEntity = mock(HttpEntity.class);
            httpHeads = new Header[0];
            when(statusLine.getStatusCode()).thenReturn(200);
            when(statusLine.toString()).thenReturn("200OK");

            headers = new Header[0];
            url = "http://127.0.0.1";
            head = new HashMap<>();
            urlParam = new HashMap<>();
            body = "{}";
            history = new HookOneHistory();
            history.setUrl(url);
            history.setRequestParams("{}");
            history.setRequestBody("{}");

            httpClient = mock(CloseableHttpClient.class);
            response = mock(CloseableHttpResponse.class);
            when(response.getStatusLine()).thenReturn(statusLine);
            when(response.getEntity()).thenReturn(httpEntity);
            when(response.getAllHeaders()).thenReturn(httpHeads);

            when(httpClient.execute(any(HttpPost.class))).thenReturn(response);
            when(log.isDebugEnabled()).thenReturn(false);
            doNothing().when(log).debug(anyString(), anyString(), anyString());
            doNothing().when(log).debug(anyString(), anyString(), anyString());
            doNothing().when(log).error(anyString(), anyString(), anyString(), anyInt());
            doNothing().when(log).error(anyString(), anyString(), anyString(), any(Exception.class));
        }

        @Test
        void testNormal() {
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
        @Test
        void testException() throws IOException {
            when(httpClient.execute(any(HttpPost.class))).thenAnswer(a -> {
                throw new IOException("time out");
            });
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
        @Test
        void testRetryTimesLessOne() {
            webHookHttpUtil.setHttpRetryTimes(-1);
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
        @Test
        void testWithHistory() {
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(history);
                Assertions.assertNotNull(post);
            }
        }

        @Test
        void testStatusCodeLessThan200() {
            when(statusLine.getStatusCode()).thenReturn(100);
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
        @Test
        void testStatusCodeMoreThan300() {
            when(statusLine.getStatusCode()).thenReturn(404);
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
        @Test
        void testIsDebugEnabled() {
            when(log.isDebugEnabled()).thenReturn(true);
            when(statusLine.getStatusCode()).thenReturn(404);
            try(MockedStatic<HttpClients> hc = mockStatic(HttpClients.class);
                MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class);
                MockedStatic<EntityUtils> eu = mockStatic(EntityUtils.class)) {
                ts.when(() -> TapSimplify.toJson(any(Object.class))).thenReturn("{}");
                hc.when(HttpClients::createDefault).thenReturn(httpClient);
                eu.when(() -> EntityUtils.toString(httpEntity, WebHookHttpUtil.UTF_8)).thenReturn("200OK");
                HookOneHistory post = webHookHttpUtil.post(url, urlParam, head, body);
                Assertions.assertNotNull(post);
            }
        }
    }

    @Nested
    class UtilsTest {
        @Nested
        class testGetAllHeadTest {
            Header[] headers;
            @Test
            void testNormal() {
                headers = new Header[]{new BasicHeader("K", "V")};
                String allHead = WebHookHttpUtil.Utils.getAllHead(headers);
                Assertions.assertEquals("K: V", allHead);
            }
        }

        @Nested
        class ToHeadsTest {
            @Test
            void testNormal() {
                String h = "K:V\nkkkkk\nkk:\n:vv";
                Header[] headers = WebHookHttpUtil.Utils.toHeads(h);
                Assertions.assertNotNull(headers);
                Assertions.assertEquals(2, headers.length);
            }
        }

        @Nested
        class ToHeadsFromMapTest {
            @Test
            void testNormal() {
                Map<String, Object> head = new HashMap<>();
                head.put("h", "v");
                Header[] headers = WebHookHttpUtil.Utils.toHeads(head);
                Assertions.assertNotNull(headers);
                Assertions.assertEquals(2, headers.length);
            }
        }

        @Nested
        class AddContentTypeTest {
            @Test
            void testNotContainsContentTyep() {
                Map<String, Object> head = new HashMap<>();
                head.put("Content-Type", "application/json");
                Header[] headers1 = new Header[0];
                Header[] headers = WebHookHttpUtil.Utils.addContentType(headers1, head);
                Assertions.assertNotNull(headers);
                Assertions.assertEquals(headers1, headers);
            }
        }

        @Nested
        class AddHeardParamsTest {
            HttpPost post;
            @Test
            void testNormal() {
                post = new HttpPost();
                WebHookHttpUtil.Utils.addHeardParams(post, new Header[0]);
                Assertions.assertEquals(0, post.getAllHeaders().length);
            }
            @Test
            void testHeadsIsNull() {
                post = new HttpPost();
                WebHookHttpUtil.Utils.addHeardParams(post, null);
                Assertions.assertEquals(0, post.getAllHeaders().length);
            }
            @Test
            void testHeadsIsReSet() {
                post = new HttpPost();
                Header[] h = new Header[2];
                h[0] = new BasicHeader("User-Agent", "v");
                h[1] = h[0];
                WebHookHttpUtil.Utils.addHeardParams(post, h);
                Assertions.assertEquals(1, post.getAllHeaders().length);
            }
            @Test
            void testREQUEST_METHOD() {
                post = new HttpPost();
                Header[] h = new Header[2];
                h[0] = new BasicHeader(WebHookHttpUtil.REQUEST_METHOD, "v");
                h[1] = new BasicHeader("REQUEST_xMETHOD", "v");
                WebHookHttpUtil.Utils.addHeardParams(post, h);
                Assertions.assertEquals(1, post.getAllHeaders().length);
            }
        }

        @Nested
        class AddUrlParamToUrlTest {
            @Test
            void testNormal() {
                Assertions.assertEquals("127.0.0.1?key=1", WebHookHttpUtil.Utils.addUrlParamToUrl("127.0.0.1?", "key=1"));
            }
            @Test
            void testNormal1() {
                Assertions.assertEquals("127.0.0.1?key=1", WebHookHttpUtil.Utils.addUrlParamToUrl("127.0.0.1?", "key=1"));
            }
        }

        @Nested
        class GetUrlParamTest {
            @Test
            void testNormal() {
                Map<String, Object> p = new HashMap<>();
                p.put("key", "1");
                Assertions.assertEquals("key=1", WebHookHttpUtil.Utils.getUrlParam(p));
            }
            @Test
            void testValueIsNull() {
                Map<String, Object> p = new HashMap<>();
                p.put("key", null);
                Assertions.assertEquals("key=", WebHookHttpUtil.Utils.getUrlParam(p));
            }
            @Test
            void testNormal1() {
                Assertions.assertEquals("", WebHookHttpUtil.Utils.getUrlParam(null));
            }
        }

        @Nested
        class GetRequestBodyTest {
            @Test
            void tesNull() {
                Assertions.assertNull(WebHookHttpUtil.Utils.getRequestBody(null));
            }
        }
    }
}