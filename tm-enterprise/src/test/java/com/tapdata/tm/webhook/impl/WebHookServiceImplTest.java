package com.tapdata.tm.webhook.impl;

import com.tapdata.tm.Unit4Util;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.utils.MessageUtil;
import com.tapdata.tm.webhook.dto.HookOneHistoryDto;
import com.tapdata.tm.webhook.dto.WebHookInfoDto;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import com.tapdata.tm.webhook.entity.WebHookEvent;
import com.tapdata.tm.webhook.entity.WebHookInfo;
import com.tapdata.tm.webhook.enums.PingResult;
import com.tapdata.tm.webhook.repository.WebHookRepository;
import com.tapdata.tm.webhook.server.WebHookAdapterService;
import com.tapdata.tm.webhook.server.WebHookHttpUtilService;
import com.tapdata.tm.webhook.vo.WebHookInfoVo;
import io.tapdata.entity.simplify.TapSimplify;
import jakarta.servlet.http.HttpServletRequest;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WebHookServiceImplTest {
    WebHookServiceImpl webHookService;
    WebHookAdapterService webHookAdapter;
    WebHookHttpUtilService webHookHttpUtil;
    WebHookRepository webHookRepository;
    UserDetail user;
    Logger log;
    @BeforeEach
    void init() {
        log = mock(Logger.class);
        user = mock(UserDetail.class);
        webHookService = mock(WebHookServiceImpl.class);
        webHookHttpUtil = mock(WebHookHttpUtilService.class);
        webHookAdapter = mock(WebHookAdapterService.class);
        webHookRepository = mock(WebHookRepository.class);

        doCallRealMethod().when(webHookService).setWebHookRepository(webHookRepository);
        doCallRealMethod().when(webHookService).setWebHookHttpUtil(webHookHttpUtil);
        doCallRealMethod().when(webHookService).setWebHookAdapter(webHookAdapter);
        webHookService.setWebHookAdapter(webHookAdapter);
        webHookService.setWebHookHttpUtil(webHookHttpUtil);
        webHookService.setWebHookRepository(webHookRepository);
        Unit4Util.mockSlf4jLog(webHookService, log);
    }

    @Nested
    class BeforeSaveTest {
        WebHookInfoDto dto;
        @BeforeEach
        void init() {
            dto = new WebHookInfoDto();
            doCallRealMethod().when(webHookService).beforeSave(dto, user);
            when(user.getUserId()).thenReturn("userId");
        }

        @Test
        void testNormal() {
            webHookService.beforeSave(dto, user);
            Assertions.assertNotNull(dto.getId());
            Assertions.assertEquals("userId", dto.getUserId());
        }
        @Test
        void testObjectIdIsNull() {
            dto.setId(new ObjectId());
            webHookService.beforeSave(dto, user);
            Assertions.assertNotNull(dto.getId());
            Assertions.assertEquals("userId", dto.getUserId());
        }
    }

    @Nested
    class ListTest {
        Filter filter;
        Locale locale;
        Page<WebHookInfoDto> webHookInfoDtoPage;
        List<WebHookInfoDto> all;

        @BeforeEach
        void init() {
            filter = mock(Filter.class);
            locale = mock(Locale.class);
            all = new ArrayList<>();
            webHookInfoDtoPage = mock(Page.class);
            when(webHookInfoDtoPage.getTotal()).thenReturn(1L);
            when(webHookInfoDtoPage.getItems()).thenReturn(all);
            when(webHookService.find(filter)).thenReturn(webHookInfoDtoPage);

            when(webHookService.list(filter, user, locale)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            Page<WebHookInfoVo> list = webHookService.list(filter, user, locale);
            Assertions.assertNotNull(list);
            Assertions.assertNotNull(list.getItems());
            Assertions.assertEquals(0, list.getItems().size());
            Assertions.assertEquals(1L, list.getTotal());
        }

        @Test
        void testNotEmpty() {
            when(webHookService.dtoToVo(any(), any())).thenReturn(new WebHookInfoVo());
            all.add(null);
            all.add(new WebHookInfoDto());
            Page<WebHookInfoVo> list = webHookService.list(filter, user, locale);
            Assertions.assertNotNull(list);
            Assertions.assertNotNull(list.getItems());
            Assertions.assertEquals(1, list.getItems().size());
            Assertions.assertEquals(1L, list.getTotal());
        }
        @Test
        void testNotEmpty2() {
            when(webHookService.dtoToVo(any(), any())).thenReturn(null);
            all.add(null);
            all.add(new WebHookInfoDto());
            Page<WebHookInfoVo> list = webHookService.list(filter, user, locale);
            Assertions.assertNotNull(list);
            Assertions.assertNotNull(list.getItems());
            Assertions.assertEquals(0, list.getItems().size());
            Assertions.assertEquals(1L, list.getTotal());
        }
    }

    @Nested
    class FindWebHookByHookIdTest {
        @Test
        void testNormal() {
            when(webHookService.findOne(any(Query.class), any(UserDetail.class))).thenReturn(new WebHookInfoDto());
            when(webHookService.dtoToVo(any(WebHookInfoDto.class), any(WebHookInfoVo.class))).thenReturn(new WebHookInfoVo());
            when(webHookService.findWebHookByHookId("id", user)).thenCallRealMethod();
            WebHookInfoVo id = webHookService.findWebHookByHookId("id", user);
            Assertions.assertNotNull(id);
            verify(webHookService).findOne(any(Query.class), any(UserDetail.class));
            verify(webHookService).dtoToVo(any(WebHookInfoDto.class), any(WebHookInfoVo.class));
            verify(webHookService).findWebHookByHookId("id", user);
        }
    }

    @Nested
    class CreateTest {
        WebHookInfoDto dto;
        WebHookInfo insert;
        @BeforeEach
        void init() {
            dto = mock(WebHookInfoDto.class);
            insert = mock(WebHookInfo.class);
            doNothing().when(webHookService).checkUrl(anyString());
            when(dto.getHookName()).thenReturn("hook name");
            when(webHookService.convertToEntity(WebHookInfo.class, dto)).thenReturn(insert);
            when(webHookRepository.insert(insert, user)).thenReturn(insert);
            when(webHookService.copy(insert)).thenReturn(mock(WebHookInfoVo.class));
            when(webHookService.create(dto, user)).thenCallRealMethod();
            when(dto.getOpen()).thenReturn(true);
            doNothing().when(dto).setOpen(true);
            doNothing().when(dto).setHookName(anyString());
        }
        @Test
        void testNormal() {
            dto.setOpen(true);
            Assertions.assertNotNull(webHookService.create(dto, user));
            verify(dto).getHookName();
            verify(webHookService).convertToEntity(WebHookInfo.class, dto);
            verify(webHookRepository).insert(insert, user);
            verify(webHookService).copy(insert);
        }

        @Test
        void testOpenIsNull() {
            when(dto.getOpen()).thenReturn(null);
            Assertions.assertNotNull(webHookService.create(dto, user));
            verify(dto).getHookName();
            verify(webHookService).convertToEntity(WebHookInfo.class, dto);
            verify(webHookRepository).insert(insert, user);
            verify(webHookService).copy(insert);
        }

        @Test
        void testHookNameIsEmpty() {
            when(dto.getHookName()).thenReturn(null);
            try(MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage("webhook.create.hook.defaultName")).thenReturn("name");
                Assertions.assertNotNull(webHookService.create(dto, user));
                verify(dto).getHookName();
                verify(webHookService).convertToEntity(WebHookInfo.class, dto);
                verify(webHookRepository).insert(insert, user);
                verify(webHookService).copy(insert);
            }
        }
    }

    @Nested
    class CheckUrlTest {
        @Test
        void testUrlIsBlank() {
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(WebHookServiceImpl.URL_INVALID_CODE, "")).thenReturn("msg");
                when(webHookHttpUtil.checkURL("")).thenReturn(true);
                doCallRealMethod().when(webHookService).checkUrl("");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkUrl("");
                    } catch (BizException e) {
                        Assertions.assertEquals(WebHookServiceImpl.URL_INVALID_CODE, e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testNormal() {
            doCallRealMethod().when(webHookService).setMaxURLLength(1000);
            webHookService.setMaxURLLength(1000);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                when(webHookHttpUtil.checkURL("http://191.110.11.1:21")).thenReturn(true);
                doCallRealMethod().when(webHookService).checkUrl("http://191.110.11.1:21");
                Assertions.assertDoesNotThrow(() -> {
                    webHookService.checkUrl("http://191.110.11.1:21");
                });
            }
        }
        @Test
        void testException() {
            doCallRealMethod().when(webHookService).setMaxURLLength(100);
            webHookService.setMaxURLLength(100);
            when(webHookHttpUtil.checkURL("url")).thenReturn(false);
            doCallRealMethod().when(webHookService).checkUrl("url");
            Assertions.assertThrows(BizException.class, () -> {
                try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                    mu.when(() -> MessageUtil.getMessage("webhook.url.invalid", "url")).thenReturn("msg");
                    webHookService.checkUrl("url");
                } catch (BizException e) {
                    Assertions.assertEquals("webhook.url.invalid", e.getErrorCode());
                    throw e;
                }
            });
        }
        @Test
        void testLooLongException() {
            doCallRealMethod().when(webHookService).setMaxURLLength(-1);
            webHookService.setMaxURLLength(-1);
            when(webHookHttpUtil.checkURL("url")).thenReturn(false);
            doCallRealMethod().when(webHookService).checkUrl("url");
            Assertions.assertThrows(BizException.class, () -> {
                try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                    mu.when(() -> MessageUtil.getMessage("webhook.url.too.long",  -1, "url".length())).thenReturn("msg");
                    webHookService.checkUrl("url");
                } catch (BizException e) {
                    Assertions.assertEquals("webhook.url.too.long", e.getErrorCode());
                    throw e;
                }
            });
        }
        @Test
        void testIsLocalHostPort() {
            doCallRealMethod().when(webHookService).setMaxURLLength(1000);
            webHookService.setMaxURLLength(1000);
            StringBuffer buffer = new StringBuffer();
            buffer.append("http://localhost:3000");
            HttpServletRequest request = mock(HttpServletRequest.class);
            ServletRequestAttributes requestAttributes = mock(ServletRequestAttributes.class);
            when(requestAttributes.getRequest()).thenReturn(request);
            when(request.getRequestURL()).thenReturn(buffer);
            doNothing().when(log).warn(anyString());
            try(MockedStatic<RequestContextHolder> rch = mockStatic(RequestContextHolder.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                rch.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);

                mu.when(() -> MessageUtil.getMessage("webhook.url.host.invalid", "localhost:3000")).thenReturn("msg");
                when(webHookHttpUtil.checkURL("http://localhost:3000/api")).thenReturn(true);
                doCallRealMethod().when(webHookService).checkUrl("http://localhost:3000/api");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkUrl("http://localhost:3000/api");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.url.host.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testPortNotEquals() {
            doCallRealMethod().when(webHookService).setMaxURLLength(1000);
            webHookService.setMaxURLLength(1000);
            StringBuffer buffer = new StringBuffer();
            buffer.append("http://localhost:3001");
            HttpServletRequest request = mock(HttpServletRequest.class);
            ServletRequestAttributes requestAttributes = mock(ServletRequestAttributes.class);
            when(requestAttributes.getRequest()).thenReturn(request);
            when(request.getRequestURL()).thenReturn(buffer);
            doNothing().when(log).warn(anyString());
            try(MockedStatic<RequestContextHolder> rch = mockStatic(RequestContextHolder.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                rch.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);

                mu.when(() -> MessageUtil.getMessage("webhook.url.host.invalid", "localhost:3000")).thenReturn("msg");
                when(webHookHttpUtil.checkURL("http://localhost:3000/api")).thenReturn(true);
                doCallRealMethod().when(webHookService).checkUrl("http://localhost:3000/api");
                Assertions.assertDoesNotThrow(() -> webHookService.checkUrl("http://localhost:3000/api"));
            }
        }
        @Test
        void testHostNotEquals() {
            doCallRealMethod().when(webHookService).setMaxURLLength(1000);
            webHookService.setMaxURLLength(1000);
            StringBuffer buffer = new StringBuffer();
            buffer.append("http://*********:3000");
            HttpServletRequest request = mock(HttpServletRequest.class);
            ServletRequestAttributes requestAttributes = mock(ServletRequestAttributes.class);
            when(requestAttributes.getRequest()).thenReturn(request);
            when(request.getRequestURL()).thenReturn(buffer);
            doNothing().when(log).warn(anyString());
            try(MockedStatic<RequestContextHolder> rch = mockStatic(RequestContextHolder.class);
                MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                rch.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);

                mu.when(() -> MessageUtil.getMessage("webhook.url.host.invalid", "localhost:3000")).thenReturn("msg");
                when(webHookHttpUtil.checkURL("http://localhost:3000/api")).thenReturn(true);
                doCallRealMethod().when(webHookService).checkUrl("http://localhost:3000/api");
                Assertions.assertDoesNotThrow(() -> webHookService.checkUrl("http://localhost:3000/api"));
            }
        }
    }

    @Nested
    class UpdatePingResultTest {
        WebHookInfoDto dto;
        @Test
        void testNormal() {
            dto = new WebHookInfoDto();
            dto.setId(new ObjectId());
            dto.setPingResult(PingResult.SUCCEED);
            when(webHookService.findOne(any(Query.class))).thenReturn(dto);
            when(webHookService.dtoToVo(any(WebHookInfoDto.class), any(WebHookInfoVo.class))).thenReturn(new WebHookInfoVo());
            when(webHookService.updatePingResult(dto)).thenCallRealMethod();
            webHookService.updatePingResult(dto);
            verify(webHookService).findOne(any(Query.class));
            verify(webHookService).dtoToVo(any(WebHookInfoDto.class), any(WebHookInfoVo.class));
        }
    }

    @Nested
    class CloseTest {
        String[] ids;
        @Test
        void testClose() {
            ids = new String[0];
            when(webHookService.close(ids, user)).thenCallRealMethod();
            when(webHookService.updateParamByIds(ids, "open", false, user)).thenReturn(mock(List.class));
            webHookService.close(ids, user);
            verify(webHookService).updateParamByIds(ids, "open", false, user);
        }
    }

    @Nested
    class DeleteTest {

    }

    @Nested
    class FindMyOpenHookInfoListTest {
        @BeforeEach
        void init() {

            when(webHookService.findAll(any(Query.class))).thenReturn(mock(List.class));
            when(webHookService.findMyOpenHookInfoList(anyString(), anyString(), anyList())).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            Assertions.assertNotNull(webHookService.findMyOpenHookInfoList("hookType", "metric", new ArrayList<>()));
            verify(webHookService).findAll(any(Query.class));
        }
        @Test
        void testHookTypeIsEmpty() {
            Assertions.assertNotNull(webHookService.findMyOpenHookInfoList("", "metric", new ArrayList<>()));
            verify(webHookService).findAll(any(Query.class));
        }
        @Test
        void testMetricIsEmpty() {
            Assertions.assertNotNull(webHookService.findMyOpenHookInfoList("hookType", "", new ArrayList<>()));
            verify(webHookService).findAll(any(Query.class));
        }
    }

    @Nested
    class PingTest {
        WebHookInfoDto webHookEvent;
        HookOneHistory send;
        WebHookEvent event;
        @BeforeEach
        void init() {
            event = new WebHookEvent();
            when(user.getUserId()).thenReturn("userId");
            webHookEvent = mock(WebHookInfoDto.class);
            when(webHookEvent.getUrl()).thenReturn("http://127.0.0.1:666");
            when(webHookHttpUtil.checkURL(anyString())).thenReturn(true);
            when(webHookEvent.getId()).thenReturn(new ObjectId());

            send = new HookOneHistory();
            send.setStatus(PingResult.SUCCEED.name());
            when(webHookAdapter.sendAndSave(event, webHookEvent)).thenReturn(send);
            when(webHookAdapter.send(event, webHookEvent)).thenReturn(send);
            when(webHookService.updatePingResult(any(WebHookInfoDto.class))).thenReturn(new WebHookInfoVo());
            when(webHookService.ping(webHookEvent, user)).thenCallRealMethod();
            when(webHookService.copy(any(HookOneHistory.class))).thenReturn(mock(HookOneHistoryDto.class));
        }
        @Test
        void testPing() {
            try (MockedStatic<WebHookEvent> whe = mockStatic(WebHookEvent.class)) {
                whe.when(WebHookEvent::of).thenReturn(event);
                HookOneHistoryDto ping = webHookService.ping(webHookEvent, user);
                Assertions.assertNotNull(ping);
                verify(webHookHttpUtil).checkURL(anyString());
                verify(webHookAdapter, times(0)).send(event, webHookEvent);
                verify(webHookAdapter).sendAndSave(event, webHookEvent);
                verify(webHookService).updatePingResult(any(WebHookInfoDto.class));
            }
        }
        @Test
        void testFailUrl() {
            when(webHookHttpUtil.checkURL(anyString())).thenReturn(false);
            try (MockedStatic<WebHookEvent> whe = mockStatic(WebHookEvent.class);
                 MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage("webhook.url.invalid")).thenReturn("");
                whe.when(WebHookEvent::of).thenReturn(event);
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.ping(webHookEvent, user);
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.url.invalid", e.getErrorCode());
                        throw e;
                    }
                });
                verify(webHookHttpUtil).checkURL(anyString());
                verify(webHookAdapter, times(0)).send(event, webHookEvent);
                verify(webHookAdapter, times(0)).sendAndSave(event, webHookEvent);
                verify(webHookService, times(0)).updatePingResult(any(WebHookInfoDto.class));
            }
        }
        @Test
        void tesIdIsNull() {
            when(webHookEvent.getId()).thenReturn(null);
            try (MockedStatic<WebHookEvent> whe = mockStatic(WebHookEvent.class)) {
                whe.when(WebHookEvent::of).thenReturn(event);
                HookOneHistoryDto ping = webHookService.ping(webHookEvent, user);
                Assertions.assertNotNull(ping);
                verify(webHookHttpUtil).checkURL(anyString());
                verify(webHookAdapter).send(event, webHookEvent);
                verify(webHookAdapter, times(0)).sendAndSave(event, webHookEvent);
                verify(webHookService, times(0)).updatePingResult(any(WebHookInfoDto.class));
            }
        }
    }

    @Nested
    class ReOpenTest {
        @BeforeEach
        void init() {
            when(webHookService.updateParamByIds(any(String[].class), anyMap(), any(UserDetail.class))).thenReturn(mock(List.class));
            when(webHookService.updateParamByIds(any(String[].class), anyString(), any(Object.class), any(UserDetail.class))).thenCallRealMethod();
            when(webHookService.reOpen(any(String[].class), any(UserDetail.class))).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> webHookService.reOpen(new String[0], user));
            verify(webHookService).updateParamByIds(any(String[].class), anyMap(), any(UserDetail.class));
        }
    }

    @Nested
    class UpdateParamByIds2Test {

    }

    @Nested
    class CheckCustomHttpHeadersTest {
        @BeforeEach
        void init() {
            doCallRealMethod().when(webHookService).setMaxHttpHeadersLength(anyInt());
            webHookService.setMaxHttpHeadersLength(1000);
            doCallRealMethod().when(webHookService).checkHeard(anyString());
            doCallRealMethod().when(webHookService).checkCustomHttpHeaders(anyString());
        }

        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> webHookService.checkCustomHttpHeaders(""));
        }

        @Test
        void testTooLong() {
            webHookService.setMaxHttpHeadersLength(0);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomHttpHeaders("xxs");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.http.headers.too.long", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testHeadIsInvalid() {
            webHookService.setMaxHttpHeadersLength(100);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomHttpHeaders("xxs");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.http.headers.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testHeadIsInvalid1() {
            webHookService.setMaxHttpHeadersLength(100);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomHttpHeaders("x x:s");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.http.headers.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testHeadIsInvalid2() {
            webHookService.setMaxHttpHeadersLength(100);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertDoesNotThrow(() -> webHookService.checkCustomHttpHeaders("xxs: xx x"));
            }
        }
        @Test
        void testHeadIsInvali3() {
            webHookService.setMaxHttpHeadersLength(100);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomHttpHeaders("xx s:");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.http.headers.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testHeadIsInvali4() {
            webHookService.setMaxHttpHeadersLength(100);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomHttpHeaders(":");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.http.headers.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
    }

    @Nested
    class CheckCustomTemplateTest {
        @BeforeEach
        void init() {
            doCallRealMethod().when(webHookService).setMaxCustomTemplateLength(anyInt());
            webHookService.setMaxCustomTemplateLength(1000);

            doCallRealMethod().when(webHookService).checkCustomTemplate(anyString());
        }

        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> webHookService.checkCustomTemplate(""));
        }

        @Test
        void testTooLong() {
            webHookService.setMaxCustomTemplateLength(0);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class);
                 MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class)) {
                ts.when(() -> TapSimplify.fromJsonObject(anyString())).thenAnswer(a -> {
                    return new HashMap<>();
                });
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomTemplate("xxs");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.template.too.long", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
        @Test
        void testHeadIsInvalid() {
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class);
                 MockedStatic<TapSimplify> ts = mockStatic(TapSimplify.class)) {
                ts.when(() -> TapSimplify.fromJsonObject(anyString())).thenAnswer(a -> {
                    throw new Exception("json failed");
                });
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyInt(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkCustomTemplate("xxs");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.custom.template.invalid", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
    }

    @Nested
    class CheckMarkTest {
        @BeforeEach
        void init() {
            doCallRealMethod().when(webHookService).setMaxMarkLength(anyInt());
            webHookService.setMaxMarkLength(1000);

            doCallRealMethod().when(webHookService).checkMark(anyString());
        }

        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> webHookService.checkMark(""));
        }

        @Test
        void testTooLong() {
            webHookService.setMaxMarkLength(0);
            try (MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                mu.when(() -> MessageUtil.getMessage(anyString())).thenReturn("msg");
                mu.when(() -> MessageUtil.getMessage(anyString(), anyString(), anyInt())).thenReturn("msg");
                Assertions.assertThrows(BizException.class, () -> {
                    try {
                        webHookService.checkMark("xxs");
                    } catch (BizException e) {
                        Assertions.assertEquals("webhook.info.mark.too.long", e.getErrorCode());
                        throw e;
                    }
                });
            }
        }
    }
}