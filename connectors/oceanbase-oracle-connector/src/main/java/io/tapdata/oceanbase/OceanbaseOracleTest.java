package io.tapdata.oceanbase;

import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.OracleTest;
import io.tapdata.oceanbase.bean.OceanbaseOracleConfig;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.exception.testItem.TapTestVersionEx;
import io.tapdata.util.NetUtil;

import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

public class OceanbaseOracleTest extends OracleTest implements AutoCloseable {

    public OceanbaseOracleTest(OceanbaseOracleConfig oceanbaseOracleConfig, Consumer<TestItem> consumer, ConnectionOptions connectionOptions) {
        super(oceanbaseOracleConfig, consumer, connectionOptions);
        jdbcContext = new OracleJdbcContext(oceanbaseOracleConfig);
    }

    protected Boolean testVersion() {
        try {
            jdbcContext.queryWithNext("SELECT version FROM v$instance", rs -> consumer.accept(testItem(TestItem.ITEM_VERSION, TestItem.RESULT_SUCCESSFULLY, "OceanBase v" + rs.getString(1))));
        } catch (Exception e) {
            consumer.accept(new TestItem(TestItem.ITEM_VERSION, new TapTestVersionEx(e), TestItem.RESULT_FAILED));
        }
        return true;
    }

    public Boolean testStreamRead() {
        try {
            NetUtil.validateHostPortWithSocket(((OceanbaseOracleConfig) commonDbConfig).getRawLogServerHost(), ((OceanbaseOracleConfig) commonDbConfig).getRawLogServerPort());
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY, "ObLogProxy is available"));
        } catch (Exception e) {
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN, "ObLogProxy is not available"));
        }
        return true;
    }
    @Override
    protected Boolean testDatasourceInstanceInfo() {
        buildDatasourceInstanceInfo(connectionOptions);
        return true;
    }
}


