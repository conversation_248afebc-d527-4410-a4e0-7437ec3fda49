package io.tapdata.oceanbase.dml;

import io.tapdata.common.JdbcContext;
import io.tapdata.common.dml.NormalRecordWriter;
import io.tapdata.connector.oracle.exception.OracleExceptionCollector;
import io.tapdata.entity.schema.TapTable;

import java.sql.Connection;
import java.sql.SQLException;

public class OceanbaseOracleRecordWriter extends NormalRecordWriter {

    public OceanbaseOracleRecordWriter(JdbcContext jdbcContext, TapTable tapTable) throws SQLException {
        super(jdbcContext, tapTable);
        exceptionCollector = new OracleExceptionCollector();
        insertRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        updateRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        deleteRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
    }

    public OceanbaseOracleRecordWriter(JdbcContext jdbcContext, Connection connection, TapTable tapTable) {
        super(jdbcContext, connection, tapTable);
        exceptionCollector = new OracleExceptionCollector();
        insertRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        updateRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        deleteRecorder = new OceanbaseOracleWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
    }
}
