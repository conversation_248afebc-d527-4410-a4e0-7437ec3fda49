## **Connection Configuration Help**

### **1. OceanBase connector description**
```
This OceanBase connector is designed for Oracle Enterprise mode

In this mode, OceanBase connector is highly compatible with Oracle and supports most of Oracle's functions. For authorization requirements related to source read and target write, you can refer to Oracle's relevant documentation
```
### **2. Supported versions**
OceanBase 4.0+

### **3. CDC prerequisites**

CDC pre requirements for OceanBase Oracle mode
- Install ObLogProxy service
- Install the ob log decoder service for TapData

### **4. ObLogProxy**
```
OBLogProxy is the incremental log proxy service of OceanBase, which can establish a connection with OceanBase and perform incremental log reads, providing downstream services with the ability to capture change data (CDC).
```

#### **4.1 ObLogProxy installation**
```
Download OceanBase Log Proxy Service Pack (OBLogProxy) from the official website

https://www.oceanbase.com/softwarecenter

rpm -i oblogproxy-{version}.{arch}.rpm

The project installation defaults to/usr/local/oblogproxy
```

#### **4.2 ObLogProxy Configuration**
The configuration file for OBLogProxy is placed by default in conf/conf.json
```
"Ob_sys_username": "
"Ob_sys_password": "
```
Find the configuration item above and modify it to the username and password encrypted with OceanBase
- Special attention
```
OBLogProxy requires configuring the user's username and password, and the user must be a sys tenant of OceanBase in order to connect.

The username here should not include the cluster name or tenant name, and must have read access to the OceanBase database under the sys tenant.
```
The encryption method is as follows:
```
./bin/logproxy - x username
./bin/logproxy - x password
```

#### **4.3 ObLogProxy startup**
```
cd /usr/local/oblogproxy

./run.sh start
```

### **5. ob-log-decoder**
```
Ob log decoder is TapData's ability to provide Change Data Capture (CDC) for downstream services by tailoring it to the OceanBase Oracle pattern and integrating it with the liboblog CDC component.
```
#### **5.1 ob log decoder configuration**
The default CDC user used in OceanBase Oracle mode is cluster_user= root@sys

#### **5.2 ob log decoder deployment and startup**
- Decompression of cdc: rpm2cpio oceanbase-cdc-4.2.1.4-104010012024030720.el7.x86_64.rpm | cpio -idv
- Copy obcdcServer to the decompressed bin directory: ${work_directory}/home/<USER>/Oceanbase/bin
- Execute export LD-LIBRARY-PATH=${work_directory}/home/<USER>/Oceanbase/lib64/
- Execution ./obcdcServer， You can specify the configuration directory by passing parameters: ./obcdcServer -p ${cdc_conf_path}