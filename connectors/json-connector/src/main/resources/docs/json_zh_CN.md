## **连接配置帮助**

### **1. 文件数据源前提说明**
- 由于文件数据源的特殊性，连接配置主要有文件协议特有的配置与文件路径。连接无法加载数据模型，模型需要在任务节点配置了文件类型对应需要的参数，方可加载，且目前一个连接配置仅对应一个模型。
- 文件数据源的增量读是通过文件通配扫描，只有新增文件或原文件的修改才能感知，扫描周期默认为1分钟。删除文件以及删除文件内容的数据同步，是不受支持的，且每次都是将涉及文件再次全量新增，通过更新条件字段达到修改的目的。

### **2. 支持文件协议**
以下文件协议路径分隔符均使用 "/"
#### **LOCAL**
local表示本地（引擎）所在的操作系统的文件
#### **FTP**
FTP（文件传输协议）可以设置文件服务器编码。
#### **SFTP**
SFTP（安全加密文件传输协议）可以设置文件服务器编码。Linux系统默认开启
#### **SMB**
SMB（文件共享协议）Windows系统支持的网络文件共享协议，兼容1.x,2.x,3.x。
- 特别留意：文件共享访问时，先选择共享目录，随后才是路径的填写。（共享目录/文件路径）
#### **S3FS**
S3FS（遵循S3协议文件系统）

### **3. 任务节点通用参数**
#### **模型名（Model）**
任务节点选择的文件构建的逻辑模型名称
#### **包含与排除通配（White&Black）**
通配只针对*模糊匹配，不支持正则表达式。包含置空表示所有文件，排除置空表示不排除。扫描文件逻辑为从包含匹配的文件中过滤掉排除匹配的文件。递归开关表示是否遍历子目录。

### **4. json文件配置与用法**
json文件数据源支持特大文件。
#### **json文件编码**
json文件中如有中文，需要关注该文件内容的编码方式。例如Linux中默认UTF-8，Windows中默认GBK。
#### **json类型**
- JSON Object
```
{
    "A1":{"id":1, "name":"Jarad"},
    "A2":{"id":2, "name":"James"}
}
```
上述json object将会转为__key,id,name三个字段的模型，__key为主键
- JSON Array
```
[
    {"id":1, "name":"Jarad"},
    {"id":2, "name":"James"}
]
```

### **5. json文件数据类型支持**
- STRING
- TEXT
- INTEGER
- NUMBER
- BOOLEAN
- DATETIME
- OBJECT
- ARRAY