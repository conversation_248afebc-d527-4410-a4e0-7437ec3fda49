{"properties": {"name": "Aliyun RDS MySQL", "icon": "icons/aliyun_rds_mysql.png", "id": "aliyun-rds-mysql", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "api_server_supported"}, {"id": "illegal_date_acceptable"}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1, "required": true}, "port": {"type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 3, "required": true}, "username": {"type": "string", "title": "${username}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 4, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 5}, "addtionalString": {"type": "string", "title": "${addtionalString}", "default": "useUnicode=yes&characterEncoding=UTF-8", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 6}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 7, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "database": "database", "username": "username", "password": "password", "extParams": "Connection Parameter String", "timezone": "timezone", "doc": "docs/aliyun_rds_mysql_en_US.md"}, "zh_CN": {"host": "地址", "port": "端口", "database": "数据库", "username": "账号", "password": "密码", "extParams": "连接参数", "timezone": "时区", "doc": "docs/aliyun_rds_mysql_zh_CN.md"}, "zh_TW": {"host": "地址", "port": "端口", "database": "數據庫", "username": "賬號", "password": "密碼", "extParams": "連接參數", "timezone": "時區", "doc": "docs/aliyun_rds_mysql_zh_TW.md"}}, "dataTypes": {"char[($byte)]": {"to": "TapString", "byte": 255, "defaultByte": 1, "byteRatio": 3, "fixed": true}, "varchar($byte)": {"name": "<PERSON><PERSON><PERSON>", "to": "TapString", "byte": 16358, "defaultByte": 1, "byteRatio": 3}, "tinytext": {"to": "TapString", "byte": 255, "pkEnablement": false}, "text": {"to": "TapString", "byte": "64k", "pkEnablement": false}, "mediumtext": {"to": "TapString", "byte": "16m", "pkEnablement": false}, "longtext": {"to": "TapString", "byte": "4g", "pkEnablement": false}, "json": {"to": "TapMap", "byte": "4g", "pkEnablement": false}, "binary[($byte)]": {"to": "TapBinary", "byte": 255, "defaultByte": 1, "fixed": true}, "varbinary[($byte)]": {"to": "TapBinary", "byte": 65532, "defaultByte": 1}, "tinyblob": {"to": "TapBinary", "byte": 255}, "blob": {"to": "TapBinary", "byte": "64k"}, "mediumblob": {"to": "TapBinary", "byte": "16m"}, "longblob": {"to": "TapBinary", "byte": "4g"}, "bit[($bit)]": {"to": "TapNumber", "bit": 64, "queryOnly": true}, "tinyint[($zerofill)]": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "tinyint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [0, 255], "unsigned": "unsigned"}, "tinyint($zerofill) [unsigned] [zerofill]": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [0, 255], "unsigned": "unsigned"}, "smallint[($zerofill)]": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "smallint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 16, "precision": 5, "value": [0, 65535], "unsigned": "unsigned"}, "mediumint[($zerofill)]": {"to": "TapNumber", "bit": 24, "precision": 7, "value": [-8388608, 8388607]}, "mediumint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 24, "precision": 8, "value": [0, 16777215], "unsigned": "unsigned"}, "int[($zerofill)]": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "int[($zerofill)] unsigned": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [0, 4294967295]}, "bigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "bigint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 64, "precision": 20, "value": [0, 18446744073709551615]}, "decimal[($precision,$scale)][unsigned]": {"to": "TapNumber", "precision": [1, 65], "scale": [0, 30], "defaultPrecision": 10, "defaultScale": 0, "unsigned": "unsigned", "fixed": true}, "float($precision,$scale)[unsigned]": {"to": "TapNumber", "name": "float", "precision": [1, 30], "scale": [0, 30], "value": ["-3.402823466E+38", "3.402823466E+38"], "unsigned": "unsigned", "fixed": false}, "float": {"to": "TapNumber", "precision": [1, 6], "scale": [0, 6], "fixed": false}, "double": {"to": "TapNumber", "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false}, "double[($precision,$scale)][unsigned]": {"to": "TapNumber", "precision": [1, 255], "scale": [0, 30], "value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "unsigned": "unsigned", "fixed": false}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "year[($fraction)]": {"to": "TapYear", "range": ["1901", "2155"], "fraction": [0, 4], "defaultFraction": 4, "pattern": "yyyy"}, "time": {"to": "TapTime", "range": ["-838:59:59", "838:59:59"]}, "datetime[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 0}, "timestamp[($fraction)]": {"to": "TapDateTime", "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 0, "withTimeZone": true}, "enum($enums)": {"name": "enum", "to": "TapString", "queryOnly": true, "byte": 16383}, "set($sets)": {"name": "set", "to": "TapString", "queryOnly": true, "byte": 16383}, "point": {"to": "TapBinary", "queryOnly": true}, "linestring": {"to": "TapBinary", "queryOnly": true}, "polygon": {"to": "TapBinary", "queryOnly": true}, "geometry": {"to": "TapBinary", "queryOnly": true}, "multipoint": {"to": "TapBinary", "queryOnly": true}, "multilinestring": {"to": "TapBinary", "queryOnly": true}, "multipolygon": {"to": "TapBinary", "queryOnly": true}, "geomcollection": {"to": "TapBinary", "queryOnly": true}}}