package io.tapdata.connector.tencent.db.table;

import io.tapdata.connector.mysql.MysqlMaker;

/**
 * <AUTHOR>
 * @description CreateTable create by <PERSON>
 * @create 2023/4/14 13:39
 **/
public abstract class CreateTable extends MysqlMaker {
    public static MysqlMaker sqlMaker(String createType, Object partitionKey) {
        switch (createType) {
            case "BroadcastTable":
                return new BroadcastTable();
            case "PartitionTable":
                return new PartitionTable().partitionKey(partitionKey);
            default:
                return new SingleTable();
        }
    }
}
