import io.tapdata.connector.db2.Db2JdbcContext;
import io.tapdata.connector.db2.config.Db2Config;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.time.Instant;

public class Main {

    public void update(int row) throws Throwable {
        Db2Config config = new Db2Config();
        config.setHost("**************");
        config.setPort(40024);
        config.setDbType("db2");
        config.setJdbcDriver("com.ibm.db2.jcc.DB2Driver");
        config.setDatabase("TESTDB");
        config.setSchema("JARAD");
        config.setUser("db2inst1");
        config.setPassword("Gotapd8!");

        try (
                Db2JdbcContext context = new Db2JdbcContext(config);
                Connection connection = context.getConnection();
//                PreparedStatement statement = connection.prepareStatement("insert into \"TAPDATA\".\"TEST_DB2_KAFKA\" values(?,?,?,?,?,?,?)")
                PreparedStatement statement = connection.prepareStatement("update \"JARAD\".\"TEST_DB2_KAFKA\" set COLUMN1=? where ID=?")
        ) {
            for(int i = 0; i < row; i++) {
                statement.setObject(2, 20000+i);
//                statement.setObject(2, "UpdateRandomString" + i);
//                statement.setObject(3, 47.29);
//                statement.setObject(4, Timestamp.from(Instant.now()));
//                statement.setObject(5, 0);
//                statement.setObject(6, 2140146444);
//                statement.setObject(7, 64.71119);
                statement.setObject(1, "111UpdateRandomString" + i);
                statement.addBatch();
            }

            statement.executeBatch();
            connection.commit();
        }
//        context.query("select * from \"JARAD\".\"TestDb2\"", resultSet -> {
//            while (resultSet.next()) {
//                Clob clob = (Clob) resultSet.getObject("POLICY_ID");
//                System.out.println(DbKit.clobToString(clob));
//            }
//        });
//        context.query("SELECT HEX(1) FROM SYSIBM.SYSDUMMY1", resultSet -> {
//            while (resultSet.next()) {
//                System.out.println(resultSet.getString(1));
//            }
//        });
//        System.out.println(ByteOrder.nativeOrder());
    }

    public void insert(int row) throws Throwable {
        Db2Config config = new Db2Config();
        config.setHost("**************");
        config.setPort(40024);
        config.setDbType("db2");
        config.setJdbcDriver("com.ibm.db2.jcc.DB2Driver");
        config.setDatabase("TESTDB");
        config.setSchema("JARAD");
        config.setUser("db2inst1");
        config.setPassword("Gotapd8!");

        try (
                Db2JdbcContext context = new Db2JdbcContext(config);
                Connection connection = context.getConnection();
                PreparedStatement statement = connection.prepareStatement("insert into \"JARAD\".\"TEST_DB2_KAFKA\" values(?,?,?,?,?,?,?)")
//                PreparedStatement statement = connection.prepareStatement("update \"TAPDATA\".\"TEST_DB2_KAFKA\" set COLUMN1=? where ID=?")
        ) {
            for(int i = 0; i < row; i++) {
                statement.setObject(1, 20000+i);
                statement.setObject(2, "UpdateRandomString" + i);
                statement.setObject(3, 47.29);
                statement.setObject(4, Timestamp.from(Instant.now()));
                statement.setObject(5, 0);
                statement.setObject(6, 2140146444);
                statement.setObject(7, 64.71119);
//                statement.setObject(1, "Update222RandomString" + i);
                statement.addBatch();
            }

            statement.executeBatch();
            connection.commit();
        }
    }

    public void test() throws Throwable {
        insert(55);
        update(55);
    }
}
