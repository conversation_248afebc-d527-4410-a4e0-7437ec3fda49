package io.tapdata.connector.db2.cdc.util;

public class BytesUtil {

    public static byte[] getSubBytes(byte[] src, int start, int end, int length, boolean needReverse) {
        byte[] bytes = new byte[length];
        end = Math.min(src.length, end);
        if (end - start >= 0) System.arraycopy(src, start, bytes, needReverse ? 2 : 0, end - start);
        return bytes;
    }

    public static byte[] hexStringToByteArray(String hexString) {
        hexString = hexString.replaceAll(" ", "");
        int len = hexString.length();
        byte[] bytes = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            bytes[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4) + Character
                    .digit(hexString.charAt(i + 1), 16));
        }
        return bytes;
    }

}
