package io.tapdata.connector.db2.cdc.offset;

import java.io.Serializable;

public class Db2Offset implements Serializable {

    private String sortString;
    private Long offsetValue;
    private String lastScn;
    private String pendingScn;
    private Long timestamp;

    public Db2Offset() {

    }

    public Db2Offset(String sortString, Long offsetValue) {
        this.sortString = sortString;
        this.offsetValue = offsetValue;
    }

    public String getSortString() {
        return sortString;
    }

    public void setSortString(String sortString) {
        this.sortString = sortString;
    }

    public Long getOffsetValue() {
        return offsetValue;
    }

    public void setOffsetValue(Long offsetValue) {
        this.offsetValue = offsetValue;
    }

    public String getLastScn() {
        return lastScn;
    }

    public void setLastScn(String lastScn) {
        this.lastScn = lastScn;
    }

    public String getPendingScn() {
        return pendingScn;
    }

    public void setPendingScn(String pendingScn) {
        this.pendingScn = pendingScn;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}
