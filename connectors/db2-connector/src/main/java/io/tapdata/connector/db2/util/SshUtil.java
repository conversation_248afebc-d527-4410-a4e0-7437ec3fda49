package io.tapdata.connector.db2.util;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import io.tapdata.connector.db2.config.Db2Config;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

public class SshUtil {

    public static byte[] exec(Db2Config db2Config, String command, Supplier<Boolean> stoppingSupplier) throws Exception {
        return exec(db2Config.getHost(), db2Config.getSshPort(), db2Config.getUser(), db2Config.getPassword(), command, stoppingSupplier);
    }

    public static byte[] exec(String host, int port, String user, String pass, String command, Supplier<Boolean> stoppingSupplier) throws Exception {
        JSch jSch = new JSch();
        Session session = jSch.getSession(user, host, port);
        try {
            session.setPassword(pass);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setTimeout(150000);
            session.connect(100000);

            AtomicBoolean isToStop = new AtomicBoolean(false);
            Supplier<Boolean> toStop = () -> {
                if (stoppingSupplier.get()) {
                    return true;
                }
                return isToStop.get();
            };
            ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
            try {
                channelExec.setCommand(command);
                channelExec.setInputStream(null);
                channelExec.setErrStream(null);

                Map<String, StreamResult> resultMap = new HashMap<>();
                watchInputStream(channelExec, "input", resultMap::put, toStop);
                watchInputStream(channelExec, "error", resultMap::put, toStop);

                channelExec.connect(50000);

                StreamResult streamResult;
                while (resultMap.size() != 2) {
                    if (isToStop.get()) {
                        throw new InterruptedException("Exit with stop");
                    }
                    streamResult = resultMap.get("error");
                    if (null != streamResult) {
                        byte[] result = streamResult.getResult();
                        throw new RuntimeException("Exec error: " + new String(result));
                    }
                    Thread.sleep(TimeUnit.MILLISECONDS.toMillis(500));
                }
                streamResult = resultMap.get("error");
                if (null != streamResult) {
                    byte[] result = streamResult.getResult();
                    throw new RuntimeException("Exec error: " + new String(result));
                }
                streamResult = resultMap.get("input");
                if (null != streamResult) {
                    return streamResult.getResult();
                }
                return null;
            } finally {
                isToStop.set(true);
                if (null != channelExec) channelExec.disconnect();
            }
        } finally {
            if (null != session) session.disconnect();
        }
    }

    public static void watchInputStream(ChannelExec channelExec, String type, BiConsumer<String, StreamResult> consumer, Supplier<Boolean> stopSupplier) {
        new Thread(() -> {
            StreamResult streamResult = null;
            try (
                    InputStream input = "input".equals(type) ? channelExec.getInputStream() : channelExec.getErrStream();
                    ByteArrayOutputStream baos = new ByteArrayOutputStream()
            ) {
                int bufSize = 1024;
                int len;
                byte[] buf = new byte[bufSize];
                while (!Thread.interrupted()) {
                    if (stopSupplier.get()) return;

                    len = input.read(buf);
                    if (-1 == len) {
                        break;
                    } else if (0 == len) {
                        Thread.sleep(10);
                    } else {
                        baos.write(buf, 0, len);

                    }
                }
                if (baos.size() != 0) {
                    streamResult = new StreamResult(baos.toByteArray(), null);
                }
            } catch (Exception e) {
                streamResult = new StreamResult(null, e);
            } finally {
                consumer.accept(type, streamResult);
            }
        }).start();
    }

    private static class StreamResult {
        private final byte[] bytes;
        private final Exception error;

        public StreamResult(byte[] bytes, Exception error) {
            this.bytes = bytes;
            this.error = error;
        }

        public byte[] getResult() throws Exception {
            if (null != error) {
                throw error;
            } else {
                return bytes;
            }
        }
    }

    public static String executeCommand(String host, int port, String user, String pass, String command) {
        JSch jSch = new JSch();
        StringBuilder res = new StringBuilder();
        Session session = null;
        ChannelExec channelExec = null;
        try {
            session = jSch.getSession(user, host, port);
            session.setPassword(pass);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setTimeout(150000);
            session.connect(100000);
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);
            channelExec.setInputStream(null);
            channelExec.setErrStream(System.err);
            channelExec.connect(50000);
            try (
                    InputStream input = channelExec.getInputStream()
            ) {
                byte[] tmp = new byte[1024];
                int time = 0, size = 100, len = 3000 * size;
                boolean hasResult = false;
                while (true) {
                    while (input.available() > 0) {
                        time = 0;
                        int i = input.read(tmp, 0, 1024);
                        hasResult = true;
                        if (i < 0) {
                            break;
                        }
                        res.append(new String(tmp, 0, i));
                    }
                    if (input.available() == 0 && hasResult) {
                        break;
                    }
                    try {
                        Thread.sleep(size);
                    } catch (Exception ignored) {
                    }
                    time += size;
                    if (time >= len) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("ssh execute command failed: " + command, e);
        } finally {
            if (null != channelExec) {
                channelExec.disconnect();
            }
            if (null != session) {
                session.disconnect();
            }
        }
        return res.toString();
    }
}