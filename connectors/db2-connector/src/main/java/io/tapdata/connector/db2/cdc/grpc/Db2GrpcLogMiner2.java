package io.tapdata.connector.db2.cdc.grpc;

import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NegotiationType;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.tapdata.common.cdc.ILogMiner;
import io.tapdata.common.cdc.NormalRedo;
import io.tapdata.common.concurrent.ConcurrentProcessor;
import io.tapdata.common.concurrent.TapExecutors;
import io.tapdata.connector.db2.Db2JdbcContext;
import io.tapdata.connector.db2.cdc.Db2LogMiner;
import io.tapdata.connector.db2.cdc.offset.Db2Offset;
import io.tapdata.connector.db2.cdc.parser.ColumnInfo;
import io.tapdata.connector.db2.cdc.parser.DB297Parser;
import io.tapdata.connector.db2.cdc.parser.DefaultDB2LogBytesConverter;
import io.tapdata.connector.db2.cdc.parser.ParseColumnBytesResult;
import io.tapdata.data.db2.*;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;

import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static io.tapdata.base.ConnectorBase.list;

public class Db2GrpcLogMiner2 extends Db2LogMiner implements ILogMiner {

    private ManagedChannel channel;
    private DB2ReadLogServerGrpc.DB2ReadLogServerBlockingStub blockingStub;
    private ReadLogRequest readLogRequest;
    private ReadLogRequest.Builder requestBuilder;
    private TaskHandleRequest taskHandleRequest;
    private boolean isBigEndian;
    private boolean needReverse;
    private Charset charset;
    private static final MessageHeader MESSAGE_HEADER = MessageHeader.newBuilder().setProtocolVersion(1).build();
    private ConcurrentProcessor<ReadLogPayload, NormalRedo> concurrentProcessor;

    private static final DB297Parser parser = new DB297Parser();
    private static final DefaultDB2LogBytesConverter converter = new DefaultDB2LogBytesConverter();
    private final Map<String, List<ColumnInfo>> tablesColumns = new HashMap<>();
    private final Map<String, String> tableIds = new HashMap<>();
    private final Map<String, String> partitionIds = new HashMap<>(); //Map<tableSpaceId-partitionId, tableSpaceId-tableId>

    public Db2GrpcLogMiner2(Db2JdbcContext db2JdbcContext, String connectorId, Log tapLogger) {
        super(db2JdbcContext, connectorId, tapLogger);
        concurrentProcessor = TapExecutors.createSimple(8, 32, "Db2GrpcLogMiner-Processor");
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        getEndian();
        getCharset();
        converter.setNeedReverse(needReverse);
        converter.setCharset(charset);
        channel = NettyChannelBuilder.forAddress(db2Config.getRawLogServerHost(), db2Config.getRawLogServerPort())
                .maxInboundMessageSize(Integer.MAX_VALUE)
                .negotiationType(NegotiationType.PLAINTEXT)
                .keepAliveWithoutCalls(true)
                .keepAliveTimeout(30, TimeUnit.SECONDS)
                .build();
        blockingStub = DB2ReadLogServerGrpc.newBlockingStub(channel);
        taskHandleRequest = TaskHandleRequest.newBuilder().setHeader(MESSAGE_HEADER).setId(connectorId).build();
        ReaderSource.Builder sourceBuilder = ReaderSource.newBuilder().setDatabaseName(db2Config.getDatabase())
                .setDatabaseHostname(db2Config.getHost())
                .setDatabaseServiceName(String.valueOf(db2Config.getPort()))
                .setDatabaseUsername(db2Config.getUser())
                .setDatabasePassword(db2Config.getPassword());
        requestBuilder = ReadLogRequest.newBuilder()
                .setHeader(MESSAGE_HEADER)
                .setBigEndian(isBigEndian)
                .setId(connectorId)
                .setSource(sourceBuilder.build());
        if (EmptyKit.isNotEmpty(db2Offset.getPendingScn())) {
            requestBuilder.setScn(db2Offset.getPendingScn());
            tapLogger.info("pending lri to C: {}", db2Offset.getPendingScn());
        } else if (EmptyKit.isNotEmpty(db2Offset.getLastScn())) {
            requestBuilder.setScn(db2Offset.getLastScn());
            tapLogger.info("current lri to C: {}", db2Offset.getLastScn());
        } else {
            requestBuilder.setStime(db2Offset.getTimestamp() / 1000);
        }
        List<String> partitionTables = new ArrayList<>();
        db2JdbcContext.queryAllTables(tableList).forEach(table -> {
            int tableId = Integer.parseInt(table.getString("TABLEID"));
            int tableSpaceId = Integer.parseInt(table.getString("TBSPACEID"));
            if (tableId >= 0) {
                requestBuilder.addTables(SourceTable.newBuilder().setTableId(tableId)
                        .setTableSpaceId(tableSpaceId).build());
                partitionIds.put(tableSpaceId + "-" + tableId, tableSpaceId + "-" + tableId);
            } else {
                partitionTables.add(table.getString("TABNAME"));
            }
        });
        if (EmptyKit.isNotEmpty(partitionTables)) {
            db2JdbcContext.query(String.format(DB2_PARTITION_INFO, db2Config.getSchema(),
                    partitionTables.stream().map(table -> "'" + table + "'").collect(Collectors.joining(","))), resultSet -> {
                while (resultSet.next()) {
                    int partitionId = Integer.parseInt(resultSet.getString("PARTITIONOBJECTID"));
                    int tableSpaceId = Integer.parseInt(resultSet.getString("TBSPACEID"));
                    requestBuilder.addTables(SourceTable.newBuilder().setTableId(partitionId)
                            .setTableSpaceId(Integer.parseInt(resultSet.getString("TBSPACEID"))).build());
                    partitionIds.put(tableSpaceId + "-" + partitionId, tableSpaceId + "-" + Integer.parseInt(resultSet.getString("TABLEID")));
                }
            });
        }
        readLogRequest = requestBuilder.setTarget(WriterTarget.newBuilder().setType(WriterType.GRPC).build()).build();
        getColumnInfo();
    }

    private void getColumnInfo() throws Throwable {
        db2JdbcContext.query(String.format(DB2_COLUMN_INFO, tableList.stream().map(table -> "'" + table + "'").collect(Collectors.joining(",")), db2Config.getSchema()), resultSet -> {
            while (resultSet.next()) {
                ColumnInfo columnInfo = new ColumnInfo(resultSet);
                String tableName = columnInfo.getTableName();
                Integer tableId = columnInfo.getTableId();
                Integer tableSpaceId = columnInfo.getTableSpaceId();
                if (!tablesColumns.containsKey(tableName)) {
                    tablesColumns.put(tableName, new ArrayList<>());
                    tableIds.put(tableSpaceId + "-" + tableId, tableName);
                }
                tablesColumns.get(tableName).add(columnInfo);
            }
        });
    }

    protected void ddlFlush() throws Throwable {
        tableIds.clear();
        tablesColumns.clear();
        getColumnInfo();
        makeLobTables();
    }

    @Override
    public void startMiner() {
        isRunning.set(true);
//        initRedoLogQueueAndThread();
        ControlResponse response = blockingStub.createReadLogTask(readLogRequest);
        if (response.getCode() == ResponseCode.ALREADY_CREATE) {
            if (ResponseCode.OK == blockingStub.deleteReadLogTask(taskHandleRequest).getCode()) {
                response = blockingStub.createReadLogTask(readLogRequest);
            }
        }
        if (response.getCode() != ResponseCode.OK) {
            throw new IllegalArgumentException(response.getMsg());
        }
        PingResponse pingResponse = blockingStub.ping(PingRequest.getDefaultInstance());
        if (!"ok".equals(pingResponse.getMsg())) {
            throw new IllegalArgumentException("Grpc Log Miner service is not available");
        }
        Iterator<ReadLogResponse> logResponseIterator = blockingStub.pullReadLog(taskHandleRequest);
        Thread t = new Thread(() -> {
            AtomicReference<List<TapEvent>> events = new AtomicReference<>(list());
            NormalRedo lastRedo = null;
            int heartbeat = 0;
            long lastTimestamp = 0L;
            while (isRunning.get()) {
                try {
                    NormalRedo redo = concurrentProcessor.get(100, TimeUnit.MILLISECONDS);
                    if (EmptyKit.isNotNull(redo)) {
                        lastTimestamp = Math.max(redo.getTimestamp(), lastTimestamp);
                        redo.setTimestamp(lastTimestamp == 0L ? System.currentTimeMillis() : lastTimestamp);
                        if (ReadLogOp.HEARTBEAT.name().equals(redo.getOperation())) {
                            if (heartbeat++ > 5) {
                                Db2Offset db2Offset = new Db2Offset();
                                if (EmptyKit.isNotEmpty(redo.getCdcSequenceStr())) {
                                    db2Offset.setLastScn(redo.getCdcSequenceStr());
                                    db2Offset.setPendingScn(redo.getCdcPendingStr());
                                } else {
                                    db2Offset.setTimestamp(redo.getTimestamp());
                                }
                                consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(redo.getTimestamp())), db2Offset);
                                heartbeat = 0;
                            }
                            continue;
                        }
                        lastRedo = redo;
                        createEvent(redo, events, redo.getTimestamp());
                        if (events.get().size() >= recordSize) {
                            submitEvent(redo, events.get());
                            events.set(new ArrayList<>());
                        }
                    } else {
                        if (events.get().size() > 0) {
                            submitEvent(lastRedo, events.get());
                            events.set(new ArrayList<>());
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                    return;
                }
            }
        });
        t.setName("Db2GrpcLogMiner-Consumer");
        t.start();
        while (logResponseIterator.hasNext()) {
            if (EmptyKit.isNotNull(threadException.get())) {
                throw new RuntimeException(threadException.get());
            }
            ReadLogResponse logResponse = logResponseIterator.next();
            logResponse.getPayloadList().forEach(payload -> {
                while (ddlStop.get() && isRunning.get()) {
                    TapSimplify.sleep(500);
                }
                if (payload.getOp() == ReadLogOp.DDL) {
                    ddlStop.set(true);
                }
                concurrentProcessor.runAsync(payload, e -> {
                    try {
                        return emit(e);
                    } catch (Exception er) {
                        threadException.set(er);
                        return null;
                    }
                });
            });
        }
        if (isRunning.get()) {
            throw new RuntimeException("Exception occurs in Grpc Log Miner service");
        }
    }

    private NormalRedo emit(ReadLogPayload payload) {
        NormalRedo redo = new NormalRedo();
        redo.setCdcSequenceStr(payload.getScn());
        redo.setCdcPendingStr(payload.getPendingMinScn());
        redo.setTransactionId(payload.getTransactionId());
        redo.setRid(payload.getRid());
        redo.setTimestamp(payload.getTransactionTime() * 1000);
        ReadLogOp op = payload.getOp();

        redo.setOperation(op.name());
        redo.setNameSpace(payload.getSchema());
        String tableName = tableIds.get(partitionIds.get(payload.getTableSpaceId() + "-" + payload.getTableId()));
        if ((null == tableName || !tableList.contains(tableName)) && "INSERT, UPDATE, DELETE".contains(op.name()))
            return null;
        redo.setTableName(tableName);
        try {
            switch (op.name()) {
                case "DDL":
                    redo.setSqlRedo(payload.getLogBytes().toStringUtf8());
                    break;
                case "INSERT":
                    redo.setRedoRecord(parseByteRecord(tableName, payload.getLogBytes().toByteArray()));
                    if (lobTables.containsKey(tableName)) {
                        lookupLob(tableName, redo);
                    }
                    break;
                case "UPDATE":
                    if (payload.getLogBytes().toByteArray().length <= 4) {
                        return null;
                    } else {
                        redo.setRedoRecord(parseByteRecord(tableName, payload.getLogBytes().toByteArray()));
                        redo.setUndoRecord(parseByteRecord(tableName, payload.getBeforeLogBytes().toByteArray()));
                    }
                    break;
                case "DELETE":
                    if (payload.getLogBytes().toByteArray().length > 4) {
                        redo.setRedoRecord(parseByteRecord(tableName, payload.getLogBytes().toByteArray()));
                    }
                    break;
            }
        } catch (Exception e) {
            throw new RuntimeException("Payload parse failed: " + e.getMessage()
                    + ", op: " + payload.getOp()
                    + ", scn: " + payload.getScn()
                    + ", rid: " + payload.getRid()
                    + ", transactionId: " + payload.getTransactionId()
                    + ", transactionTime: " + payload.getTransactionTime()
                    + ", tableId: " + payload.getTableId()
                    + ", tableName: " + tableName
                    + ", tableSpaceId: " + payload.getTableSpaceId()
                    + ", schema: " + payload.getSchema()
                    + ", logBytes: " + Base64.getEncoder().encodeToString(payload.getLogBytes().toByteArray())
                    + ", beforeLogBytes: " + Base64.getEncoder().encodeToString(payload.getBeforeLogBytes().toByteArray())
                    , e);
        }
        return redo;
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                NormalRedo redo;
                int heartbeat = 0;
                while (isRunning.get()) {
                    try {
                        redo = logQueue.poll(1, TimeUnit.SECONDS);
                        if (redo == null) {
                            continue;
                        }
                    } catch (Exception e) {
                        break;
                    }
                    try {
                        if (ReadLogOp.HEARTBEAT.name().equals(redo.getOperation())) {
                            if (heartbeat++ > 5) {
                                Db2Offset db2Offset = new Db2Offset();
                                if (EmptyKit.isNotEmpty(redo.getCdcSequenceStr())) {
                                    db2Offset.setLastScn(redo.getCdcSequenceStr());
                                    db2Offset.setPendingScn(redo.getCdcPendingStr());
                                } else {
                                    db2Offset.setTimestamp(redo.getTimestamp());
                                }
                                consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(redo.getTimestamp())), db2Offset);
                                heartbeat = 0;
                            }
                            continue;
                        }
                        // process and callback
                        processOrBuffRedo(redo, this::sendTransaction);

                    } catch (Throwable e) {
                        e.printStackTrace();
                        consumer.streamReadEnded();
                    }
                }
            });
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        ControlResponse response = blockingStub.deleteReadLogTask(taskHandleRequest);
        concurrentProcessor.close();
        if (response.getCode() != ResponseCode.OK) {
            throw new IllegalArgumentException(response.getMsg());
        }
        if (!channel.isShutdown()) {
            channel.shutdown();
        }
    }

    private void getEndian() throws Throwable {
        db2JdbcContext.query("SELECT HEX(1) FROM SYSIBM.SYSDUMMY1", resultSet -> {
            if (resultSet.next()) {
                isBigEndian = !resultSet.getString(1).equals("01000000");
                ByteOrder db2ByteOrder = isBigEndian ? ByteOrder.BIG_ENDIAN : ByteOrder.LITTLE_ENDIAN;
                needReverse = db2ByteOrder != ByteOrder.nativeOrder();
            }
        });
    }

    private void getCharset() throws SQLException {
        db2JdbcContext.query("select value from SYSIBMADM.DBCFG where name='codepage'", resultSet -> {
            if (resultSet.next()) {
                switch (resultSet.getString(1)) {
                    case "1386":
                        charset = Charset.forName("GBK");
                        break;
                    case "1200":
                        charset = StandardCharsets.UTF_16;
                        break;
                    case "819":
                        charset = StandardCharsets.ISO_8859_1;
                        break;
                    default:
                        charset = StandardCharsets.UTF_8;
                }
            }
        });
    }

    private void lookupLob(String tableName, NormalRedo redoLogContent) throws SQLException {
        Map<String, Object> redo = redoLogContent.getRedoRecord();
        Collection<String> unique = lobTables.get(tableName).primaryKeys(true);
        preparedStatement = db2JdbcContext.getConnection().prepareStatement(
                "SELECT * FROM \"" + db2Config.getSchema() + "\".\"" + tableName + "\" WHERE " + unique.stream().map(v -> "\"" + v + "\"=?").collect(Collectors.joining(" AND ")));
        int pos = 1;
        for (String field : unique) {
            preparedStatement.setObject(pos++, redo.get(field));
        }
        ResultSet rs = preparedStatement.executeQuery();
        List<String> columnNames = DbKit.getColumnsFromResultSet(rs);
        //db2 need to get BLOB,CLOB value first
        List<String> columnTypeNames = DbKit.getColumnTypesFromResultSet(rs);
        if (rs.next()) {
            DataMap dataMap = DbKit.getRowFromResultSet(rs, columnNames);
            for (int i = 0; i < columnNames.size(); i++) {
                String columnName = columnNames.get(i);
                String columnTypeName = columnTypeNames.get(i);
                if ("CLOB".equals(columnTypeName)) {
                    dataMap.put(columnName, DbKit.clobToString(rs.getClob(columnName)));
                } else if ("BLOB".equals(columnTypeName)) {
                    dataMap.put(columnName, DbKit.blobToBytes(rs.getBlob(columnName)));
                } else if ("XML".equals(columnTypeName)) {
                    dataMap.put(columnName, rs.getSQLXML(columnName).getString());
                }
            }
            redoLogContent.setRedoRecord(dataMap);
        }
    }

    private Map<String, Object> convert(List<ParseColumnBytesResult> results) {
        Map<String, Object> data = new HashMap<>();
        results.forEach(r -> {
            ColumnInfo columnInfo = r.getColumnInfo();
            if (columnInfo.getDataType().contains("LOB")) {
                return;
            }
            try {
                Object obj = converter.convert(columnInfo, r.getColumnBytes(), db2Config.getZoneOffsetHour());
                data.put(columnInfo.getColumn(), obj);
            } catch (Exception e) {
                String dataType = columnInfo.getDataType();
                String base64Value = Base64.getEncoder().encodeToString(r.getColumnBytes());
                String errorMessage = String.format("Convert '%s'.'%s' of type '%s' value is '%s', error: %s"
                        , columnInfo.getTableName(), columnInfo.getColumn(), dataType, base64Value, e.getMessage()
                );
                throw new RuntimeException(errorMessage, e);
            }
        });
        return data;
    }

    private Map<String, Object> parseByteRecord(String tableName, byte[] bytes) {
        List<ParseColumnBytesResult> results = parser.parse(tablesColumns.get(tableName), bytes, needReverse);
        return convert(results);
    }

    private final static String DB2_COLUMN_INFO = "SELECT\n" +
            "  C.TABNAME,\n" +
            "  C.COLNAME,\n" +
            "  C.COLNO,\n" +
            "  C.TYPENAME,\n" +
            "  C.LENGTH,\n" +
            "  C.SCALE,\n" +
            "  C.NULLS,\n" +
            "  T.TABLEID," +
            "  T.TBSPACEID\n" +
            "FROM SYSCAT.TABLES AS T,\n" +
            "  SYSCAT.COLUMNS AS C\n" +
            "WHERE C.TABNAME IN (%s) AND T.TABSCHEMA = '%s'\n" +
            "      AND C.TABSCHEMA = T.TABSCHEMA\n" +
            "      AND C.TABNAME = T.TABNAME\n" +
            "ORDER BY C.COLNO";

    private final static String DB2_PARTITION_INFO = "SELECT P.PARTITIONOBJECTID, P.TBSPACEID, T.TABLEID\n" +
            "FROM SYSCAT.DATAPARTITIONS P\n" +
            "         JOIN SYSCAT.TABLES T ON T.TABSCHEMA = P.TABSCHEMA AND T.TABNAME = P.TABNAME\n" +
            "WHERE P.TABSCHEMA = '%s'\n" +
            "  AND P.TABNAME IN (%s)";

}
