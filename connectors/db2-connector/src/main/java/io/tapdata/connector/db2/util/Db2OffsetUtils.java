package io.tapdata.connector.db2.util;

import io.tapdata.connector.db2.Db2JdbcContext;
import io.tapdata.connector.db2.cdc.util.BytesUtil;
import io.tapdata.connector.db2.cdc.util.NumberConvertUtil;
import io.tapdata.connector.db2.config.Db2Config;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2023/7/11 17:35 Create
 */
public class Db2OffsetUtils {

    public static String findLogFileWithDate(Db2Config db2Config, Instant date, Supplier<Boolean> stopSupplier) {
        return findLogFileWithDate(
                db2Config.getHost(),
                db2Config.getSshPort(),
                db2Config.getUser(),
                db2Config.getPassword(),
                db2Config.getDb2Profile(),
                db2Config.getArchiveDir(),
                date,
                stopSupplier
        );
    }

    public static String findLogFileWithDate(String host, int port, String user, String password, String db2Profile, String archiveDir, Instant date, Supplier<Boolean> stopSupplier) {
        long seconds = date.getEpochSecond();

        String calcTimeExpr = "nowTimes=$(date +%s) && expr " + seconds + " / 60 - $nowTimes / 60 - 1";
        String commands = String.format(". %s && cd '%s' && find . -type f -name '*.LOG' -cmin `%s`|head -n1", db2Profile, archiveDir, calcTimeExpr);
        try {
            byte[] results = SshUtil.exec(host, port, user, password, commands, stopSupplier);
            if (null != results) {
                String result = new String(results);
                if (result.startsWith("./")) {
                    return result.substring(2);
                }
                throw new RuntimeException("Invalid result format: " + new String(results));
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("Find log file failed, %s, commands: %s", e.getMessage(), commands), e);
        }
        return null;
    }

    public static Instant queryPendingFirstDate(Db2JdbcContext db2JdbcContext) {
        try {
            // If not exists pending transactions return null
            AtomicReference<Timestamp> atomicDate = new AtomicReference<>();
            db2JdbcContext.queryWithNext("select min(UOW_START_TIME) as UOW_START_TIME" +
                            " from TABLE(MON_GET_UNIT_OF_WORK(NULL, -1)) as t" +
                            " where UOW_STOP_TIME is NULL" +
                            " and WORKLOAD_OCCURRENCE_STATE = 'UOWWAIT'" +
                            " and ROWS_MODIFIED > 0"
                    , rs -> atomicDate.set(rs.getTimestamp(1))
            );
            if (null != atomicDate.get()) {
                TimeZone timeZone = db2JdbcContext.getTimeZone();
                if (null != timeZone) {
                    ZoneOffset zoneOffset = ZoneOffset.ofTotalSeconds(timeZone.getRawOffset());
                    return atomicDate.get().toLocalDateTime().toInstant(zoneOffset);
                }
                return atomicDate.get().toInstant();
            }
            return null;
        } catch (SQLException e) {
            throw new RuntimeException("Find pending transaction first date failed: " + e.getMessage(), e);
        }
    }

    public static String getEndLriByFlsn(Db2Config db2Config, Integer logFileNum, Supplier<Boolean> stopSupplier) {
        String commands = String.format(". %s && cd %s && db2flsn -lrirange -startlog %s -endlog %s -logpath2 %s"
                , db2Config.getDb2Profile(), db2Config.getSqlCtlDir(), logFileNum, logFileNum, db2Config.getArchiveDir()
        );
        try {
            // ********.LOG: has LRI range 0000000000000001000000000001B78E0000000000E42432 to 0000000000000001000000000001B9960000000000E4298C
            byte[] results = SshUtil.exec(db2Config, commands, stopSupplier);
            if (null != results) {
                String result = new String(results).trim();
                Pattern p = Pattern.compile(".*: has LRI range ([^ ]*) to ([^ ]*)");
                Matcher m = p.matcher(result);
                if (m.find()) {
                    return flsn2Lri(m.group(2), false); // get end lri
                }
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String flsn2Lri(String str, boolean littleEndian) {
        if (str.length() != 48) throw new RuntimeException("Illegal flsn: " + str);

        String part1 = str.substring(32);
        String part2 = str.substring(16, 32);
        String part3 = str.substring(0, 16);
        long scn = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part1), 0, littleEndian);
        long transactionId = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part2), 0, littleEndian);
        long chainId = NumberConvertUtil.bytesToLong(BytesUtil.hexStringToByteArray(part3), 0, littleEndian);
        return chainId + "," + transactionId + "," + scn;
    }

    public static String getLriByDb2Fmtlog(Db2Config db2Config, Integer logFileNum) {
        return getLriByDb2Fmtlog(
                db2Config.getHost(),
                db2Config.getSshPort(),
                db2Config.getUser(),
                db2Config.getPassword(),
                db2Config.getDb2Profile(),
                db2Config.getArchiveDir(),
                logFileNum
        );
    }

    public static String getLriByDb2Fmtlog(String host, int port, String user, String password, String db2Profile, String archiveDir, Integer logFileNum) {
        Db2FmtlogInfo info = db2Fmtlog(host, port, user, password, db2Profile, archiveDir, logFileNum);
        if (null == info) {
            return null;
        } else {
            return info.toLri();
        }
    }

    private static Db2FmtlogInfo db2Fmtlog(String host, int port, String user, String password, String db2Profile, String archiveDir, Integer logFileNum) {
        String commands = String.format(". %s && cd '%s' && db2fmtlog %d", db2Profile, archiveDir, logFileNum);

        //Log File S0011045.LOG:
        //   Extent Number              11045
        //   Format Version             14
        //   Architecture Level Version V:11 R:5 M:5 F:0 I:0 SB:0
        //   Encrypted                  No
        //   Compression Mode           OFF
        //   Number of Pages            55
        //   Partition                  0
        //   Log Stream                 0
        //   Database Seed              1736206624
        //   Log File Chain ID          1
        //   Previous Extent ID         2023-07-04-03.00.17.000000 GMT
        //   Current Extent ID          2023-07-04-04.00.05.000000 GMT
        //   Database log ID            2022-04-26-02.20.31.000000 GMT
        //   Topology Life ID           2022-04-26-02.20.31.000000 GMT
        //   First LFS/LSN              6765949/0000000012718C73
        //   Last LFS/LSN               6766116/00000000127193FA
        //   LSO range                  319409153185 to 319409377364
        String result = SshUtil.executeCommand(host, port, user, password, commands);
        Db2FmtlogInfo info = null;
        try {
            result = result.trim();
            if (!result.startsWith("Log File ")) {
                throw new RuntimeException(String.format("Db2fmtlog %s@%s:%d failed, commands: `%s`, result: %s"
                        , user, host, port, commands, result)
                );
            }

            int currentIndex = -1;
            for (String line : result.split("\n")) {
                currentIndex++;
                if (line.startsWith("Log File ")) {
                    if (null == info) {
                        info = new Db2FmtlogInfo();
                    } else {
                        throw new RuntimeException("Have more results, currentIndex: " + currentIndex);
                    }
                    info.logFile = line.substring(9, line.length() - 1);
                } else if (null == info) {
                    throw new RuntimeException(String.format("Not start with 'Log File ', line: %d", currentIndex));
                } else if (line.startsWith("   Log File Chain ID")) {
                    info.chainId = Integer.parseInt(line.substring("   Log File Chain ID".length()).trim());
                } else if (line.startsWith("   Previous Extent ID")) {
                    info.previousExtentId = line.substring("   Previous Extent ID".length()).trim();
                } else if (line.startsWith("   Current Extent ID")) {
                    info.currentExtentId = line.substring("   Current Extent ID".length()).trim();
                } else if (line.startsWith("   First LFS/LSN")) {
                    info.firstLfsLsn = line.substring("   First LFS/LSN".length()).trim();
                } else if (line.startsWith("   Last LFS/LSN")) {
                    info.lastLfsLsn = line.substring("   Last LFS/LSN".length()).trim();
                }
            }

            return info;
        } catch (RuntimeException e) {
            throw new RuntimeException(String.format("%s %s@%s:%d `%s`, result: %s"
                    , e.getMessage(), user, host, port, commands, result
            ), e);
        }
    }

    private static class Db2FmtlogInfo {
        private String logFile;
        private Integer chainId;
        private String previousExtentId;
        private String currentExtentId;
        private String firstLfsLsn;
        private String lastLfsLsn;

        public String toLri() {
            String[] arr = firstLfsLsn.split("/");
            return String.format("%s,%s,%s", chainId, arr[0], Long.parseLong(arr[1], 16));
        }

        @Override
        public String toString() {
            return "Db2FmtlogInfo{" +
                    "logFile='" + logFile + '\'' +
                    ", chainId=" + chainId +
                    ", previousExtentId='" + previousExtentId + '\'' +
                    ", currentExtentId='" + currentExtentId + '\'' +
                    ", firstLfsLsn='" + firstLfsLsn + '\'' +
                    ", lastLfsLsn='" + lastLfsLsn + '\'' +
                    '}';
        }
    }
}
