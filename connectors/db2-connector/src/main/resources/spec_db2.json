{"properties": {"name": "DB2", "icon": "icons/db2.png", "doc": "${doc}", "id": "db2", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists", "log_on_nonexists"]}, {"id": "batch_read_hash_split"}, {"id": "source_support_exactly_once"}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1}, "port": {"required": true, "type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 3}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-index": 4}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 6}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 7}, "rawLogServerHost": {"type": "string", "title": "${rawLogServerHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "rawLogServerHost", "x-index": 8, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!==\"target\" ? null:\"none\"}}"}}}}, "rawLogServerPort": {"type": "string", "default": 50051, "title": "${rawLogServerPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "rawLogServerPort", "x-index": 9, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!==\"target\" ? null:\"none\"}}"}}}}, "OPTIONAL_FIELDS": {"type": "void", "properties": {"autoLri": {"type": "boolean", "title": "${autoLri}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"target": "*(sshPort,db2Profile,archived)", "fulfill": {"state": {"visible": "{{$self.value===false}}"}}}], "x-index": 10}, "sshPort": {"required": true, "type": "string", "default": 22, "title": "${sshPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 11}, "db2Profile": {"required": true, "type": "string", "title": "${db2Profile}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 12}, "archived": {"type": "boolean", "title": "${archived}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"target": "*(archiveDir,sqlCtlDir)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}], "x-index": 13}, "archiveDir": {"required": true, "type": "string", "title": "${archiveDir}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 14}, "sqlCtlDir": {"required": true, "type": "string", "title": "${sqlCtlDir}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 15}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 16}, "initialLri": {"type": "string", "title": "${initialLri}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "initialLri", "x-index": 17}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${timezoneTip}"}, "x-index": 90, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}}, "node": {"type": "object", "properties": {"hashSplit": {"type": "boolean", "title": "${hashSplit}", "default": false, "x-index": 10, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${hashSplitTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maxSplit": {"required": true, "type": "string", "title": "${maxSplit}", "default": 20, "x-index": 11, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 2, "max": 10000}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "batchReadThreadSize": {"required": true, "type": "string", "title": "${batchReadThreadSize}", "default": 4, "x-index": 12, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 1, "max": 16}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/db2_en_US.md", "host": "DB Address", "port": "Port", "database": "Service name", "schema": "schema", "extParams": "Connection String Params", "user": "user", "password": "password", "rawLogServerHost": "grpc server host", "rawLogServerPort": "grpc server port", "autoLri": "Auto Lri", "sshPort": "Ssh Port", "db2Profile": "DB2 Profile", "archived": "Is Archive Log", "archiveDir": "Archive Dir", "sqlCtlDir": "LFH Dir", "initialLri": "Initial Lri", "timezone": "timezone", "timezoneTip": "Specify the time zone, otherwise no time zone processing will be done", "hashSplit": "Hash split", "hashSplitTooltip": "When the switch is turned on, it can be sharded according to the hash value, suitable for large table full-stage sharded synchronization", "maxSplit": "Maximum number of splits", "batchReadThreadSize": "Batch read thread size"}, "zh_CN": {"doc": "docs/db2_zh_CN.md", "host": "数据库地址", "port": "端口", "database": "服务名", "schema": "模型", "extParams": "其他连接串参数", "user": "账号", "password": "密码", "rawLogServerHost": "裸日志服务器地址", "rawLogServerPort": "裸日志服务器端口", "autoLri": "自动挖掘Lri", "sshPort": "ssh端口", "db2Profile": "DB2环境配置", "archived": "是否归档", "archiveDir": "归档目录", "sqlCtlDir": "LFH控制目录", "timezone": "时区", "timezoneTip": "指定时区，否则不做时区处理", "initialLri": "初始Lri", "hashSplit": "哈希分片", "hashSplitTooltip": "开关打开时，可以根据哈希值进行分片，适用于大表全量阶段分片同步", "maxSplit": "最大分片数", "batchReadThreadSize": "批量读取线程数"}, "zh_TW": {"doc": "docs/db2_zh_TW.md", "host": "數據庫地址", "port": "端口", "database": "服務名", "schema": "模型", "extParams": "其他連接串參數", "user": "賬號", "password": "密碼", "rawLogServerHost": "裸日誌服務器信息", "rawLogServerPort": "裸日誌服務器端口", "autoLri": "自動挖掘Lri", "sshPort": "ssh端口", "db2Profile": "DB2環境配寘", "archived": "是否歸檔", "archiveDir": "歸檔目錄", "sqlCtlDir": "LFH控制目錄", "timezone": "時區", "timezoneTip": "指定時區，否則不做時區處理", "initialLri": "初始Lri", "hashSplit": "哈希分片", "hashSplitTooltip": "開關打開時，可以根據哈希值進行分片，適用於大表全量階段分片同步", "maxSplit": "最大分片數", "batchReadThreadSize": "批量讀取線程數"}}, "dataTypes": {"CHARACTER[($byte)]": {"byte": 255, "priority": 1, "defaultByte": 1, "fixed": true, "to": "TapString"}, "VARCHAR($byte)": {"byte": 32672, "priority": 1, "preferByte": 2000, "to": "TapString"}, "LONG VARCHAR": {"byte": 32700, "queryOnly": true, "to": "TapString"}, "SMALLINT": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "INTEGER": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "BIGINT": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "DECIMAL[($precision,$scale)]": {"precision": [1, 31], "scale": [0, 31], "fixed": true, "preferPrecision": 20, "defaultPrecision": 5, "preferScale": 8, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "REAL": {"value": ["-3.402823466E+38", "3.402823466E+38"], "scale": 37, "preferScale": 8, "preferPrecision": 12, "priority": 3, "queryOnly": true, "to": "TapNumber"}, "DOUBLE": {"value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "preferPrecision": 20, "preferScale": 8, "scale": 307, "priority": 3, "to": "TapNumber"}, "BOOLEAN": {"bit": 1, "priority": 1, "to": "TapBoolean"}, "DATE": {"range": ["0001-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd", "priority": 1, "to": "TapDate"}, "TIME": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "priority": 1, "to": "TapTime"}, "TIMESTAMP[($fraction)]": {"range": ["0001-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 12], "defaultFraction": 6, "withTimeZone": false, "priority": 2, "to": "TapDateTime"}, "CLOB[($byte)]": {"byte": "2147483647", "pkEnablement": false, "defaultByte": 1048576, "preferByte": 1048576, "priority": 2, "to": "TapString"}, "BLOB[($byte)]": {"byte": "2147483647", "pkEnablement": false, "defaultByte": 1048576, "preferByte": 1048576, "priority": 2, "to": "TapBinary"}, "XML": {"queryOnly": true, "to": "TapString"}, "GRAPHIC[($byte)]": {"queryOnly": true, "to": "TapBinary"}, "VARGRAPHIC[($byte)]": {"queryOnly": true, "to": "TapBinary"}, "LONG VARGRAPHIC": {"queryOnly": true, "to": "TapBinary"}, "DBCLOB[($byte)]": {"queryOnly": true, "to": "TapBinary"}}}