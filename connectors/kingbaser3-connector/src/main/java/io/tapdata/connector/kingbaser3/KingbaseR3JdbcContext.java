package io.tapdata.connector.kingbaser3;

import io.tapdata.connector.kingbaser3.config.KingbaseR3Config;
import io.tapdata.connector.postgres.PostgresJdbcContext;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class KingbaseR3JdbcContext extends PostgresJdbcContext {

    private final String databaseMode;

    public KingbaseR3JdbcContext(KingbaseR3Config config) {
        super(config);
        databaseMode = queryMode();
    }

    public String queryMode() {
        AtomicReference<String> mode = new AtomicReference<>("");
        try {
            if (Integer.parseInt(queryVersion()) > 90500) {
                return "pg";
            } else {
                queryWithNext(KINGBASE_MODE_QUERY, resultSet -> mode.set(resultSet.getInt(1) == 0 ? "oracle" : "pg"));
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return mode.get();
    }

    public String getDatabaseMode() {
        return databaseMode;
    }

    @Override
    protected String queryAllTablesSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        return String.format("pg".equals(databaseMode) ? PG_ALL_TABLE : KINGBASE_ORACLE_ALL_TABLE, getConfig().getDatabase(), schema, tableSql);
    }

    @Override
    protected String queryAllColumnsSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        return String.format("pg".equals(databaseMode) ? PG_ALL_COLUMN : KINGBASE_ORACLE_ALL_COLUMN, getConfig().getDatabase(), schema, tableSql);
    }

    @Override
    protected String queryAllIndexesSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        return String.format("pg".equals(databaseMode) ? PG_ALL_INDEX : KINGBASE_ORACLE_ALL_INDEX, getConfig().getDatabase(), schema, tableSql);
    }

    private static final String KINGBASE_MODE_QUERY = "select count(1) from information_schema.tables where table_name='pg_class'";
    private final static String KINGBASE_ORACLE_ALL_TABLE =
            "SELECT t.table_name \"tableName\",\n" +
                    "       (select max(cast(obj_description(relfilenode, 'SYS_CLASS') as varchar)) as \"tableComment\"\n" +
                    "        from sys_class c\n" +
                    "        where relname = t.table_name)\n" +
                    "FROM information_schema.tables t WHERE t.table_type='BASE TABLE' and t.table_catalog='%s' AND t.table_schema='%s' %s ORDER BY t.table_name";
    private final static String KINGBASE_ORACLE_ALL_COLUMN =
            "SELECT\n" +
                    "    col.table_name \"tableName\",\n" +
                    "    col.column_name \"columnName\",\n" +
                    "    col.column_default \"columnDefault\",\n" +
                    "    col.is_nullable \"nullable\",\n" +
                    "       (SELECT max(d.description)\n" +
                    "        FROM sys_catalog.sys_class c,\n" +
                    "             sys_description d\n" +
                    "        WHERE c.relname = col.table_name\n" +
                    "          AND d.objoid = c.oid\n" +
                    "          AND d.objsubid = col.ordinal_position) AS \"columnComment\",\n" +
                    "       (SELECT sys_catalog.format_type(a.atttypid, a.atttypmod) AS \"dataType\"\n" +
                    "        FROM sys_catalog.sys_attribute a\n" +
                    "        WHERE a.attnum > 0\n" +
                    "          AND a.attname = col.column_name\n" +
                    "          AND NOT a.attisdropped\n" +
                    "          AND a.attrelid =\n" +
                    "              (SELECT max(cl.oid)\n" +
                    "               FROM sys_catalog.sys_class cl\n" +
                    "               WHERE cl.relname = col.table_name))\n" +
                    "FROM information_schema.columns col\n" +
                    "WHERE col.table_catalog = '%s'\n" +
                    "  AND col.table_schema = '%s' %s\n" +
                    "ORDER BY col.table_name, col.ordinal_position";
    private final static String KINGBASE_ORACLE_ALL_INDEX =
            "SELECT\n" +
                    "    t.relname AS \"tableName\",\n" +
                    "    i.relname AS \"indexName\",\n" +
                    "    a.attname AS \"columnName\",\n" +
                    "    (CASE WHEN ix.indisunique THEN '1' ELSE '0' END) AS \"isUnique\",\n" +
                    "    (CASE WHEN ix.indisprimary THEN '1' ELSE '0' END) AS \"isPk\",\n" +
                    "    (CASE WHEN ix.indoption[row_number() over (partition by t.relname,i.relname order by a.attnum) - 1] & 1 = 0 THEN '1' ELSE '0' END) AS \"isAsc\"\n" +
                    "FROM\n" +
                    "    sys_class t,\n" +
                    "    sys_class i,\n" +
                    "    sys_index ix,\n" +
                    "    sys_attribute a,\n" +
                    "    information_schema.tables tt\n" +
                    "WHERE\n" +
                    "        t.oid = ix.indrelid\n" +
                    "  AND i.oid = ix.indexrelid\n" +
                    "  AND a.attrelid = t.oid\n" +
                    "  AND a.attnum = ANY(ix.indkey)\n" +
                    "  AND t.relkind = 'r'\n" +
                    "  AND tt.table_name=t.relname\n" +
                    "  AND tt.table_catalog='%s'\n" +
                    "  AND tt.table_schema='%s' %s\n" +
                    "ORDER BY t.relname, i.relname, a.attnum";

}
